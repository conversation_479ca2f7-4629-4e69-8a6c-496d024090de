package it.masterzen.Dungeon;

import it.masterzen.minebomb.*;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.CraftItemEvent;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryType;
import org.bukkit.event.inventory.PrepareItemCraftEvent;
import org.bukkit.inventory.CraftingInventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.Recipe;
import org.bukkit.inventory.ShapedRecipe;

import java.util.Arrays;

/**
 * Custom crafting system for Refillable Mine Bombs using Dungeon Finder custom ores.
 * Handles creation, cooldown reduction, and tier upgrades.
 */
public class CustomCraftingSystem implements Listener {

    // Crafting patterns
    private static final int[] PLUS_PATTERN = {1, 3, 4, 5, 7}; // + shape slots in 3x3 grid
    private static final int[] FURNACE_PATTERN = {0, 1, 2, 3, 5, 6, 7, 8}; // All except center

    @EventHandler
    public void onPrepareCraft(PrepareItemCraftEvent event) {
        CraftingInventory inventory = event.getInventory();
        ItemStack[] matrix = inventory.getMatrix();

        // Check for custom crafting recipes
        ItemStack result = checkCustomRecipes(matrix);
        if (result != null) {
            inventory.setResult(result);
        }
    }

    // Alternative approach: Use InventoryClickEvent as backup
    @EventHandler(priority = EventPriority.HIGH)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getInventory() instanceof CraftingInventory)) return;
        if (event.getSlot() != 0) return; // Only handle result slot clicks
        if (!(event.getWhoClicked() instanceof Player)) return;

        CraftingInventory craftingInventory = (CraftingInventory) event.getInventory();
        ItemStack result = craftingInventory.getResult();

        // Verify this is a custom recipe before allowing the craft
        if (result != null && checkCustomRecipes(craftingInventory.getMatrix()) != null) {
            // Allow the crafting to proceed
            return;
        } else {
            if (result != null) {
                // check if there's only custom ores
                boolean onlyCustomOres = true;
                for (ItemStack item : craftingInventory.getMatrix()) {
                    if (item != null && !CustomOreManager.isCustomOre(item)) {
                        onlyCustomOres = false;
                        break;
                    }
                }
                if (onlyCustomOres) {
                    craftingInventory.setResult(null);
                    // Send message to player to explain that the items amount must be exactly 1
                    ((Player) event.getWhoClicked()).sendMessage("§cYou can only craft 1 item at a time with custom ores");
                    return;
                }
            }
        }

        // Check if player is trying to use custom ores in vanilla recipes
        for (ItemStack item : craftingInventory.getMatrix()) {
            if (item != null && CustomOreManager.isCustomOre(item)) {
                // Cancel vanilla crafting with custom ores
                event.setCancelled(true);
                return;
            }
        }
    }

    /**
     * Check all custom crafting recipes and return the result if a match is found
     */
    private ItemStack checkCustomRecipes(ItemStack[] matrix) {
        if (matrix.length != 9) return null;

        // Check base bomb creation recipes
        ItemStack baseBombResult = checkBaseBombRecipes(matrix);
        if (baseBombResult != null) return baseBombResult;

        // Check cooldown reduction recipes
        ItemStack cooldownResult = checkCooldownReductionRecipes(matrix);
        if (cooldownResult != null) return cooldownResult;

        // Check tier upgrade recipes
        ItemStack upgradeResult = checkTierUpgradeRecipes(matrix);
        if (upgradeResult != null) return upgradeResult;

        return null;
    }

    /**
     * Check base bomb creation recipes
     */
    private ItemStack checkBaseBombRecipes(ItemStack[] matrix) {
        // Check 4 Energium + pattern for Money Bomb
        if (checkPlusPattern(matrix, CustomOre.ENERGIUM)) {
            return MineBombItem.createRefillable(RefillableBombType.REFILLABLE_MONEY, BombTier.T1, 30);
        }

        // Check 8 Energium furnace pattern for Token Bomb
        if (checkFurnacePattern(matrix, CustomOre.ENERGIUM)) {
            return MineBombItem.createRefillable(RefillableBombType.REFILLABLE_TOKENS, BombTier.T1, 30);
        }

        return null;
    }

    /**
     * Check cooldown reduction recipes
     */
    private ItemStack checkCooldownReductionRecipes(ItemStack[] matrix) {
        // Check for refillable bomb in center (slot 4)
        ItemStack centerItem = matrix[4];
        if (centerItem == null || !MineBombItem.isMineBomb(centerItem)) {
            return null;
        }

        // Check that center item amount is exactly 1
        if (centerItem.getAmount() != 1) {
            return null;
        }

        MineBombItem.ParsedRefillableBomb bombData = MineBombItem.parseRefillable(centerItem);
        if (bombData == null) return null;

        // Check Money Bomb + 4 Temporium pattern
        if (bombData.getType() == RefillableBombType.REFILLABLE_MONEY &&
                checkPlusPatternWithCenter(matrix, CustomOre.TEMPORIUM, centerItem)) {

            long currentCooldownMinutes = bombData.getCooldownDuration() / (60 * 1000);
            long newCooldownMinutes = Math.max(5, currentCooldownMinutes - 1);

            return MineBombItem.createRefillable(
                    RefillableBombType.REFILLABLE_MONEY,
                    bombData.getTier(),
                    (int) newCooldownMinutes
            );
        }

        // Check Token Bomb + 4 Mystrium pattern
        if (bombData.getType() == RefillableBombType.REFILLABLE_TOKENS &&
                checkPlusPatternWithCenter(matrix, CustomOre.MYSTRIUM, centerItem)) {

            long currentCooldownMinutes = bombData.getCooldownDuration() / (60 * 1000);
            long newCooldownMinutes = Math.max(5, currentCooldownMinutes - 1);

            return MineBombItem.createRefillable(
                    RefillableBombType.REFILLABLE_TOKENS,
                    bombData.getTier(),
                    (int) newCooldownMinutes
            );
        }

        return null;
    }

    /**
     * Check tier upgrade recipes
     */
    private ItemStack checkTierUpgradeRecipes(ItemStack[] matrix) {
        // Check for refillable bomb in center (slot 4)
        ItemStack centerItem = matrix[4];
        if (centerItem == null || !MineBombItem.isMineBomb(centerItem)) {
            return null;
        }

        // Check that center item amount is exactly 1
        if (centerItem.getAmount() != 1) {
            return null;
        }

        MineBombItem.ParsedRefillableBomb bombData = MineBombItem.parseRefillable(centerItem);
        if (bombData == null) return null;

        // Check T1 + 4 Voidstone → T2
        if (bombData.getTier() == BombTier.T1 &&
                checkPlusPatternWithCenter(matrix, CustomOre.VOIDSTONE, centerItem)) {

            long cooldownMinutes = bombData.getCooldownDuration() / (60 * 1000);
            return MineBombItem.createRefillable(
                    bombData.getType(),
                    BombTier.T2,
                    (int) cooldownMinutes
            );
        }

        // Check T2 + 4 Prismatic → T3
        if (bombData.getTier() == BombTier.T2 &&
                checkPlusPatternWithCenter(matrix, CustomOre.PRISMATIC, centerItem)) {

            long cooldownMinutes = bombData.getCooldownDuration() / (60 * 1000);
            return MineBombItem.createRefillable(
                    bombData.getType(),
                    BombTier.T3,
                    (int) cooldownMinutes
            );
        }

        return null;
    }

    /**
     * Check if matrix matches + pattern with specific ore type
     */
    private boolean checkPlusPattern(ItemStack[] matrix, CustomOre requiredOre) {
        // Check that + pattern slots have the required ore
        for (int slot : PLUS_PATTERN) {
            if (!isCustomOreOfType(matrix[slot], requiredOre)) {
                return false;
            }
        }

        // Check that other slots are empty and item amounts are 1
        for (int i = 0; i < 9; i++) {
            int finalI = i;
            if (!Arrays.stream(PLUS_PATTERN).anyMatch(slot -> slot == finalI)) {
                if (matrix[i] != null && matrix[i].getType() != Material.AIR) {
                    return false;
                }
            } else {
                // Check that items in the plus pattern have amount = 1
                if (matrix[i] != null && matrix[i].getAmount() != 1) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Check if matrix matches + pattern with center item and specific ore type around it
     */
    private boolean checkPlusPatternWithCenter(ItemStack[] matrix, CustomOre requiredOre, ItemStack centerItem) {
        // Check center slot (4) has the expected item and amount is 1
        if (!matrix[4].isSimilar(centerItem) || matrix[4].getAmount() != 1) {
            return false;
        }

        // Check + pattern slots (excluding center) have the required ore and amount is 1
        int[] plusWithoutCenter = {1, 3, 5, 7};
        for (int slot : plusWithoutCenter) {
            if (!isCustomOreOfType(matrix[slot], requiredOre) || matrix[slot].getAmount() != 1) {
                return false;
            }
        }

        // Check that corner slots are empty
        int[] cornerSlots = {0, 2, 6, 8};
        for (int slot : cornerSlots) {
            if (matrix[slot] != null && matrix[slot].getType() != Material.AIR) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if matrix matches furnace pattern (all slots except center)
     */
    private boolean checkFurnacePattern(ItemStack[] matrix, CustomOre requiredOre) {
        // Check that furnace pattern slots have the required ore and amount is 1
        for (int slot : FURNACE_PATTERN) {
            if (!isCustomOreOfType(matrix[slot], requiredOre) || matrix[slot].getAmount() != 1) {
                return false;
            }
        }

        // Check that center slot is empty
        if (matrix[4] != null && matrix[4].getType() != Material.AIR) {
            return false;
        }

        return true;
    }

    /**
     * Check if an ItemStack is a custom ore of the specified type
     */
    private boolean isCustomOreOfType(ItemStack item, CustomOre requiredOre) {
        if (item == null) return false;

        CustomOre oreType = CustomOreManager.getCustomOreType(item);
        return oreType == requiredOre;
    }
}
