package it.masterzen.Dungeon;

import it.masterzen.minebomb.*;
import it.masterzen.blockbreak.NBTItem;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.CraftItemEvent;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryType;
import org.bukkit.event.inventory.PrepareItemCraftEvent;
import org.bukkit.inventory.CraftingInventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.Recipe;
import org.bukkit.inventory.ShapedRecipe;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.Arrays;
import java.util.ArrayList;
import java.util.List;

/**
 * Custom crafting system for Refillable Mine Bombs using Dungeon Finder custom ores.
 * Handles creation, cooldown reduction, and tier upgrades.
 */
public class CustomCraftingSystem implements Listener {

    // Crafting patterns
    private static final int[] PLUS_PATTERN = {1, 3, 4, 5, 7}; // + shape slots in 3x3 grid
    private static final int[] FURNACE_PATTERN = {0, 1, 2, 3, 5, 6, 7, 8}; // All except center

    @EventHandler
    public void onPrepareCraft(PrepareItemCraftEvent event) {
        CraftingInventory inventory = event.getInventory();
        ItemStack[] matrix = inventory.getMatrix();

        // Check for custom crafting recipes
        ItemStack result = checkCustomRecipes(matrix);
        if (result != null) {
            inventory.setResult(result);
        }
    }

    // Alternative approach: Use InventoryClickEvent as backup
    @EventHandler(priority = EventPriority.HIGH)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getInventory() instanceof CraftingInventory)) return;
        if (event.getSlot() != 0) return; // Only handle result slot clicks
        if (!(event.getWhoClicked() instanceof Player)) return;

        CraftingInventory craftingInventory = (CraftingInventory) event.getInventory();
        ItemStack result = craftingInventory.getResult();

        // Verify this is a custom recipe before allowing the craft
        if (result != null && checkCustomRecipes(craftingInventory.getMatrix()) != null) {
            // Allow the crafting to proceed
            return;
        } else {
            if (result != null) {
                // check if there's only custom ores
                boolean onlyCustomOres = true;
                for (ItemStack item : craftingInventory.getMatrix()) {
                    if (item != null && !CustomOreManager.isCustomOre(item)) {
                        onlyCustomOres = false;
                        break;
                    }
                }
                if (onlyCustomOres) {
                    craftingInventory.setResult(null);
                    // Send message to player to explain that the items amount must be exactly 1
                    ((Player) event.getWhoClicked()).sendMessage("§cYou can only craft 1 item at a time with custom ores");
                    return;
                }
            }
        }

        // Check if player is trying to use custom ores in vanilla recipes
        for (ItemStack item : craftingInventory.getMatrix()) {
            if (item != null && CustomOreManager.isCustomOre(item)) {
                // Cancel vanilla crafting with custom ores
                event.setCancelled(true);
                return;
            }
        }
    }

    /**
     * Check all custom crafting recipes and return the result if a match is found
     */
    private ItemStack checkCustomRecipes(ItemStack[] matrix) {
        if (matrix.length != 9) return null;

        // Check base bomb creation recipes
        ItemStack baseBombResult = checkBaseBombRecipes(matrix);
        if (baseBombResult != null) return baseBombResult;

        // Check cooldown reduction recipes
        ItemStack cooldownResult = checkCooldownReductionRecipes(matrix);
        if (cooldownResult != null) return cooldownResult;

        // Check tier upgrade recipes
        ItemStack upgradeResult = checkTierUpgradeRecipes(matrix);
        if (upgradeResult != null) return upgradeResult;

        // Check pet egg recipe
        ItemStack petEggResult = checkPetEggRecipe(matrix);
        if (petEggResult != null) return petEggResult;

        // Check beetroot seed recipe
        ItemStack beetrootSeedResult = checkBeetrootSeedRecipe(matrix);
        if (beetrootSeedResult != null) return beetrootSeedResult;

        // Check discount skill recipe
        ItemStack discountSkillResult = checkDiscountSkillRecipe(matrix);
        if (discountSkillResult != null) return discountSkillResult;

        return null;
    }

    /**
     * Check base bomb creation recipes
     */
    private ItemStack checkBaseBombRecipes(ItemStack[] matrix) {
        // Check 4 Energium + pattern for Money Bomb
        if (checkPlusPattern(matrix, CustomOre.ENERGIUM)) {
            return MineBombItem.createRefillable(RefillableBombType.REFILLABLE_MONEY, BombTier.T1, 30);
        }

        // Check 8 Energium furnace pattern for Token Bomb
        if (checkFurnacePattern(matrix, CustomOre.ENERGIUM)) {
            return MineBombItem.createRefillable(RefillableBombType.REFILLABLE_TOKENS, BombTier.T1, 30);
        }

        return null;
    }

    /**
     * Check cooldown reduction recipes
     */
    private ItemStack checkCooldownReductionRecipes(ItemStack[] matrix) {
        // Check for refillable bomb in center (slot 4)
        ItemStack centerItem = matrix[4];
        if (centerItem == null || !MineBombItem.isMineBomb(centerItem)) {
            return null;
        }

        // Check that center item amount is exactly 1
        if (centerItem.getAmount() != 1) {
            return null;
        }

        MineBombItem.ParsedRefillableBomb bombData = MineBombItem.parseRefillable(centerItem);
        if (bombData == null) return null;

        // Check Money Bomb + 4 Temporium pattern
        if (bombData.getType() == RefillableBombType.REFILLABLE_MONEY &&
                checkPlusPatternWithCenter(matrix, CustomOre.TEMPORIUM, centerItem)) {

            long currentCooldownMinutes = bombData.getCooldownDuration() / (60 * 1000);
            long newCooldownMinutes = Math.max(5, currentCooldownMinutes - 1);

            return MineBombItem.createRefillable(
                    RefillableBombType.REFILLABLE_MONEY,
                    bombData.getTier(),
                    (int) newCooldownMinutes
            );
        }

        // Check Token Bomb + 4 Mystrium pattern
        if (bombData.getType() == RefillableBombType.REFILLABLE_TOKENS &&
                checkPlusPatternWithCenter(matrix, CustomOre.MYSTRIUM, centerItem)) {

            long currentCooldownMinutes = bombData.getCooldownDuration() / (60 * 1000);
            long newCooldownMinutes = Math.max(5, currentCooldownMinutes - 1);

            return MineBombItem.createRefillable(
                    RefillableBombType.REFILLABLE_TOKENS,
                    bombData.getTier(),
                    (int) newCooldownMinutes
            );
        }

        return null;
    }

    /**
     * Check tier upgrade recipes
     */
    private ItemStack checkTierUpgradeRecipes(ItemStack[] matrix) {
        // Check for refillable bomb in center (slot 4)
        ItemStack centerItem = matrix[4];
        if (centerItem == null || !MineBombItem.isMineBomb(centerItem)) {
            return null;
        }

        // Check that center item amount is exactly 1
        if (centerItem.getAmount() != 1) {
            return null;
        }

        MineBombItem.ParsedRefillableBomb bombData = MineBombItem.parseRefillable(centerItem);
        if (bombData == null) return null;

        // Check T1 + 4 Voidstone → T2
        if (bombData.getTier() == BombTier.T1 &&
                checkPlusPatternWithCenter(matrix, CustomOre.VOIDSTONE, centerItem)) {

            long cooldownMinutes = bombData.getCooldownDuration() / (60 * 1000);
            return MineBombItem.createRefillable(
                    bombData.getType(),
                    BombTier.T2,
                    (int) cooldownMinutes
            );
        }

        // Check T2 + 4 Prismatic → T3
        if (bombData.getTier() == BombTier.T2 &&
                checkPlusPatternWithCenter(matrix, CustomOre.PRISMATIC, centerItem)) {

            long cooldownMinutes = bombData.getCooldownDuration() / (60 * 1000);
            return MineBombItem.createRefillable(
                    bombData.getType(),
                    BombTier.T3,
                    (int) cooldownMinutes
            );
        }

        return null;
    }

    /**
     * Check pet egg creation recipe (8 Prismatic cores in furnace pattern)
     */
    private ItemStack checkPetEggRecipe(ItemStack[] matrix) {
        // Check 8 Prismatic cores furnace pattern for Pet Egg
        if (checkFurnacePattern(matrix, CustomOre.PRISMATIC)) {
            return createPetEgg();
        }

        return null;
    }

    /**
     * Check beetroot seed creation recipe (4 Prismatic cores + custom carrot seed in center)
     */
    private ItemStack checkBeetrootSeedRecipe(ItemStack[] matrix) {
        // Check center slot (4) has custom carrot seed
        ItemStack centerItem = matrix[4];
        if (centerItem == null || !isCustomCarrotSeed(centerItem)) {
            return null;
        }

        // Check that center item amount is exactly 1
        if (centerItem.getAmount() != 1) {
            return null;
        }

        // Check + pattern slots (excluding center) have Prismatic cores and amount is 1
        int[] plusWithoutCenter = {1, 3, 5, 7};
        for (int slot : plusWithoutCenter) {
            if (!isCustomOreOfType(matrix[slot], CustomOre.PRISMATIC) || matrix[slot].getAmount() != 1) {
                return null;
            }
        }

        // Check that corner slots are empty
        int[] cornerSlots = {0, 2, 6, 8};
        for (int slot : cornerSlots) {
            if (matrix[slot] != null && matrix[slot].getType() != Material.AIR) {
                return null;
            }
        }

        return createCustomBeetrootSeed();
    }

    /**
     * Checks if an item is a custom carrot seed
     */
    private boolean isCustomCarrotSeed(ItemStack item) {
        if (item == null || !item.hasItemMeta() || !item.getItemMeta().hasDisplayName()) {
            return false;
        }

        String displayName = item.getItemMeta().getDisplayName();
        return displayName.contains("§6§lCUSTOM CARROT");
    }

    /**
     * Check discount skill creation recipe (4 Prismatic cores + standard book in center)
     */
    private ItemStack checkDiscountSkillRecipe(ItemStack[] matrix) {
        // Check center slot (4) has standard book
        ItemStack centerItem = matrix[4];
        if (centerItem == null || centerItem.getType() != Material.BOOK) {
            return null;
        }

        // Check that center item amount is exactly 1 and has no custom modifications
        if (centerItem.getAmount() != 1 || (centerItem.hasItemMeta() && centerItem.getItemMeta().hasDisplayName())) {
            return null;
        }

        // Check + pattern slots (excluding center) have Prismatic cores and amount is 1
        int[] plusWithoutCenter = {1, 3, 5, 7};
        for (int slot : plusWithoutCenter) {
            if (!isCustomOreOfType(matrix[slot], CustomOre.PRISMATIC) || matrix[slot].getAmount() != 1) {
                return null;
            }
        }

        // Check that corner slots are empty
        int[] cornerSlots = {0, 2, 6, 8};
        for (int slot : cornerSlots) {
            if (matrix[slot] != null && matrix[slot].getType() != Material.AIR) {
                return null;
            }
        }

        return createDiscountSkillBook();
    }

    /**
     * Check if matrix matches + pattern with specific ore type
     */
    private boolean checkPlusPattern(ItemStack[] matrix, CustomOre requiredOre) {
        // Check that + pattern slots have the required ore
        for (int slot : PLUS_PATTERN) {
            if (!isCustomOreOfType(matrix[slot], requiredOre)) {
                return false;
            }
        }

        // Check that other slots are empty and item amounts are 1
        for (int i = 0; i < 9; i++) {
            int finalI = i;
            if (!Arrays.stream(PLUS_PATTERN).anyMatch(slot -> slot == finalI)) {
                if (matrix[i] != null && matrix[i].getType() != Material.AIR) {
                    return false;
                }
            } else {
                // Check that items in the plus pattern have amount = 1
                if (matrix[i] != null && matrix[i].getAmount() != 1) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Check if matrix matches + pattern with center item and specific ore type around it
     */
    private boolean checkPlusPatternWithCenter(ItemStack[] matrix, CustomOre requiredOre, ItemStack centerItem) {
        // Check center slot (4) has the expected item and amount is 1
        if (!matrix[4].isSimilar(centerItem) || matrix[4].getAmount() != 1) {
            return false;
        }

        // Check + pattern slots (excluding center) have the required ore and amount is 1
        int[] plusWithoutCenter = {1, 3, 5, 7};
        for (int slot : plusWithoutCenter) {
            if (!isCustomOreOfType(matrix[slot], requiredOre) || matrix[slot].getAmount() != 1) {
                return false;
            }
        }

        // Check that corner slots are empty
        int[] cornerSlots = {0, 2, 6, 8};
        for (int slot : cornerSlots) {
            if (matrix[slot] != null && matrix[slot].getType() != Material.AIR) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if matrix matches furnace pattern (all slots except center)
     */
    private boolean checkFurnacePattern(ItemStack[] matrix, CustomOre requiredOre) {
        // Check that furnace pattern slots have the required ore and amount is 1
        for (int slot : FURNACE_PATTERN) {
            if (!isCustomOreOfType(matrix[slot], requiredOre) || matrix[slot].getAmount() != 1) {
                return false;
            }
        }

        // Check that center slot is empty
        if (matrix[4] != null && matrix[4].getType() != Material.AIR) {
            return false;
        }

        return true;
    }

    /**
     * Check if an ItemStack is a custom ore of the specified type
     */
    private boolean isCustomOreOfType(ItemStack item, CustomOre requiredOre) {
        if (item == null) return false;

        CustomOre oreType = CustomOreManager.getCustomOreType(item);
        return oreType == requiredOre;
    }

    /**
     * Creates a Pet Egg item that hatches after breaking 35000 blocks
     */
    private ItemStack createPetEgg() {
        ItemStack petEgg = new ItemStack(Material.MONSTER_EGG, 1, (short) 98); // Ocelot spawn egg
        ItemMeta meta = petEgg.getItemMeta();
        meta.setDisplayName("§6§lPET EGG");

        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fA mysterious egg that will hatch");
        lore.add("§7| §finto a random pet after breaking");
        lore.add("§7| §e35,000 blocks §fwhile holding it!");
        lore.add("");
        lore.add("§e§lPROGRESS");
        lore.add("§e| §fBlocks Broken: §a0§f/§e35,000");
        lore.add("§e| §fRemaining: §c35,000 blocks");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fHold this egg while mining");
        lore.add("§6| §fto progress towards hatching!");
        lore.add("");

        meta.setLore(lore);
        petEgg.setItemMeta(meta);

        // Add NBT data for tracking
        NBTItem nbtItem = new NBTItem(petEgg);
        nbtItem.setCustomMeta("blocksToHatch", 35000);
        nbtItem.setCustomMeta("blocksBroken", 0);
        nbtItem.setCustomMeta("isPetEgg", true);

        return nbtItem.getItem();
    }

    /**
     * Creates a Custom Beetroot Seed that gives rewards from all 3 seed types
     */
    private ItemStack createCustomBeetrootSeed() {
        ItemStack seed = new ItemStack(Material.BEETROOT_SEEDS, 1);
        ItemMeta meta = seed.getItemMeta();
        meta.setDisplayName("§6§lCUSTOM BEETROOT §7Seed");

        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fPlant on your island to grow");
        lore.add("§7| §fcustom beetroot that gives");
        lore.add("§7| §a§lALL THREE SEED REWARDS §7when harvested!");
        lore.add("");
        lore.add("§a§lREWARDS WHEN HARVESTED");
        lore.add("§a| §f5-15 Fortune levels §7(from Wheat)");
        lore.add("§a| §f5-10 Token Greed levels §7(from Carrot)");
        lore.add("§a| §f5% Money OR Token Boost §7(from Potato)");
        lore.add("§a| §6§lAUTO-REPLANTS §7when fully grown!");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fPlant on farmland on your island");
        lore.add("§6| §fWait for it to grow, then harvest!");
        lore.add("");
        lore.add("§c§lWARNING: §7Can only be planted on your island!");
        lore.add("");

        meta.setLore(lore);
        seed.setItemMeta(meta);

        return seed;
    }

    /**
     * Creates a Discount Skill Book that provides +1% enchant discount
     */
    private ItemStack createDiscountSkillBook() {
        ItemStack book = new ItemStack(Material.BOOK, 1);
        ItemMeta meta = book.getItemMeta();
        meta.setDisplayName("§6§lDISCOUNT SKILL §7Book");

        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fA mystical book that teaches");
        lore.add("§7| §fthe art of enchantment bargaining!");
        lore.add("§7| §eRight-click §fto claim §a+1% enchant discount");
        lore.add("");
        lore.add("§a§lBENEFITS");
        lore.add("§a| §f+1% discount on ALL enchantments");
        lore.add("§a| §fStacks with other discounts");
        lore.add("§a| §fMaximum: 10% total discount");
        lore.add("");
        lore.add("§e§lHOW IT WORKS");
        lore.add("§e| §fReduces the cost of enchanting");
        lore.add("§e| §fyour pickaxe with any enchantment");
        lore.add("§e| §fApplies to Tokens, Crystals, and Beacon Points");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fRight-click this book to claim");
        lore.add("§6| §fthe discount permanently!");
        lore.add("");
        lore.add("§c§lNOTE: §7This book will be consumed on use");
        lore.add("");

        meta.setLore(lore);
        book.setItemMeta(meta);

        // Add NBT data for identification
        NBTItem nbtItem = new NBTItem(book);
        nbtItem.setCustomMeta("isDiscountSkillBook", true);
        nbtItem.setCustomMeta("discountAmount", 1);

        return nbtItem.getItem();
    }
}
