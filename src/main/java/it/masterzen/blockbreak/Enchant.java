package it.masterzen.blockbreak;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.vk2gpz.tokenenchant.api.TokenEnchantAPI;
import it.masterzen.Dungeon.CustomOre;
import it.masterzen.MongoDB.PlayerData;
import it.masterzen.PrestigeShop.GUI;
import it.masterzen.commands.Main;
import me.clip.ezblocks.EZBlocks;
import net.luckperms.api.LuckPerms;
import net.luckperms.api.model.user.User;
import net.luckperms.api.node.Node;
import org.apache.commons.lang3.StringUtils;
import org.bukkit.*;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryAction;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.FireworkEffectMeta;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.PotionMeta;
import org.bukkit.potion.Potion;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.json.simple.JSONObject;
import sun.java2d.pipe.AlphaPaintPipe;

import javax.swing.*;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Enchant implements Listener {

    private final String prefix = "§e§lENCHANT §8»§7 ";
    private final Book bookManager = new Book();
    private static final LinkedHashMap <String, EnchantList> enchantList = new LinkedHashMap<>();

    public final AlphaBlockBreak mainClass;

    public Enchant(AlphaBlockBreak plugin) {
        mainClass = plugin;
        setupEnchantList();
    }

    public void setupEnchantList() {
        // TIER 0
        enchantList.put("Efficiency", new EnchantList("Efficiency", 1000, 100000, "Tokens", false, 1, false, false, true, 0, 0));
        //enchantList.put("Unbreaking", new EnchantList("Unbreaking", 1000, 80, "Tokens", false, 1, false, false, true));
        enchantList.put("Fortune", new EnchantList("Fortune", 0, 500000, "Tokens", true, 1, false, false, false, 75000, 0.00008)); // Exponential scaling: 0.8% per level
        enchantList.put("Speed", new EnchantList("Speed", 5, 5000000, "Tokens", false, 1, false, false, false, 0));
        enchantList.put("Haste", new EnchantList("Haste", 10, 5000000, "Tokens", false, 1, false, false, false, 0));
        enchantList.put("Token Greed", new EnchantList("Token Greed", 0, 1000000, "Tokens", true, 1, false, false, false, 25000, 0.00006)); // Exponential scaling: 0.6% per level
        enchantList.put("Blessing", new EnchantList("Blessing", 1, 25000000000L, "Tokens", false, 1, false, false, true, 0, 0));

        // TIER 1
        enchantList.put("Greed", new EnchantList("Greed", 2000, 30000000, "Tokens", false, 2, false, false, true, 500, 1));
        //enchantList.put("Key Finder", new EnchantList("Key Finder", 0, 5000000, true, 2));
        enchantList.put("Key Finder", new EnchantList("Key Finder", 1000, 50000000, "Tokens", false, 2, false, false, true, 250, 1));
        enchantList.put("Combo", new EnchantList("Combo", 1000, 20000000, "Tokens", false, 2, false, false, true, 500, 1));
        enchantList.put("XPGreed", new EnchantList("XPGreed", 100, 100000000, "Tokens", false, 2, false, false, true, 50, 1));
        enchantList.put("Randomizer", new EnchantList("Randomizer", 10, 7500000000L, "Tokens", false, 2, false, false, true, 2, 1));
        enchantList.put("JackHammer", new EnchantList("JackHammer", 500, 10000000, "Tokens", false, 2, false, false, true, 250, 1));

        // TIER 2
        enchantList.put("Block Miner", new EnchantList("Block Miner", 3, 10000000000000L, "Tokens", false, 3, false, false, true, 1, 2));
        enchantList.put("RandomizerMerchant", new EnchantList("RandomizerMerchant", 5, 5000000000000L, "Tokens", false, 3, false, false, true, 1, 2));
        //enchantList.put("LuckyBlock", new EnchantList("LuckyBlock", 2, 1000000L, "Tokens", false, 3, false, false, true));
        enchantList.put("BeaconGreed", new EnchantList("BeaconGreed", 100, 10000000000L, "Tokens", false, 3, false, false, true, 20, 2));
        enchantList.put("Voucher Finder", new EnchantList("Voucher Finder", 250, 100000000, "Tokens", false, 3, false, false, true, 25, 2));
        enchantList.put("Prestige Finder", new EnchantList("Prestige Finder", 1000, 5000000, "Tokens", false, 3, false, false, true, 250, 2));
        enchantList.put("Fracture", new EnchantList("Fracture", 250, 7500000000L, "Tokens", false, 3, false, false, true, 10, 2));
        enchantList.put("FriendShip", new EnchantList("FriendShip", 100, 50000000000L, "Tokens", false, 3, false, false, true, 20, 2));
        enchantList.put("ValueFinder", new EnchantList("ValueFinder", 10, 10000000000000L, "Tokens", false, 3, true, false, false, 10, 2));

        // TIER 3
        enchantList.put("LuckyKey", new EnchantList("LuckyKey", 5, 10000000000000L, "Tokens", false, 4, false, false, true, 1, 3));
        enchantList.put("Prestige Point Finder", new EnchantList("Prestige Point Finder", 5, 10000000000000L, "Tokens", false, 4, false, false, true, 0, 3));
        enchantList.put("Robot Finder", new EnchantList("Robot Finder", 100, 500000000, "Tokens", false, 4, false, false, true, 10, 3));
        enchantList.put("JackPot", new EnchantList("JackPot", 1, 500000000000000L, "Tokens", false, 4, false, false, false, 1, 3));
        enchantList.put("Nuke", new EnchantList("Nuke", 100, 12500000000L, "Tokens", false, 4, false, false, true, 100, 3));
        enchantList.put("Timelapse", new EnchantList("Timelapse", 10, 5000000000000L, "Tokens", false, 4, false, false, true, 0, 3));
        enchantList.put("Spawner Shard Finder", new EnchantList("Spawner Shard Finder", 1, 50000000000000L, "Tokens", false, 4, false, false, true, 0, 3));

        // TIER 4
        enchantList.put("WonderLand", new EnchantList("WonderLand", 10, 20000000000000L, "Tokens", false, 5, false, false, false, 1, 4));
        enchantList.put("Fire Road", new EnchantList("Fire Road", 250, 15000000000L, "Tokens", false, 5, false, false, false, 25, 4));
        enchantList.put("Explosive", new EnchantList("Explosive", 50, 500000000000L, "Tokens", false, 5, false, false, false, 5, 4));
        enchantList.put("ArmorPoint Finder", new EnchantList("ArmorPoint Finder", 100, 250000000000L, "Tokens", false, 5, false, true, false, 5, 4));
        enchantList.put("Meteor", new EnchantList("Meteor", 50, 125000000000L, "Tokens", false, 5, true, true, false, 1,5));

        enchantList.put("Momentum", new EnchantList("Momentum", 100, 1, "Tokens", true, 6, true, true, false, 0));
        enchantList.put("Frenzy", new EnchantList("Frenzy", 100, 1, "Tokens", true, 6, true, true, false, 0));

        // UNIQUE ENCHANT
        enchantList.put("Candy Finder", new EnchantList("Candy Finder", 1, 1, "Tokens", false, 6, false, true, false, 0));

        // DUNGEON ENCHANT
        enchantList.put("Dungeon Fortune", new EnchantList("Dungeon Fortune", 3, 5000, "Crystals", false, 6, true, false, 1, false));
        enchantList.put("Dungeon Killer", new EnchantList("Dungeon Killer", 3, 1000, "Crystals", false, 6, true, false, 1, false));
        enchantList.put("Dungeon Luck", new EnchantList("Dungeon Luck", 5, 2500, "Crystals", false, 6, true, false, 2, false));
        enchantList.put("Dungeon Bomb", new EnchantList("Dungeon Bomb", 1, 2500, "Crystals", false, 6, true, false, 3, false));
        enchantList.put("Lucky Beacon", new EnchantList("Lucky Beacon", 10, 500, "Crystals", false, 6, true, false, 1, false));
        enchantList.put("Dungeon Finder", new EnchantList("Dungeon Finder", 10, 5000, "Crystals", false, 6, true, false, 1, false));

        // BEACON PICKAXE ENCHANT
        enchantList.put("Beacon Bomb", new EnchantList("Beacon Bomb", 3, 2500, "BeaconPoints", false, 6, true, false, false, 0));
        enchantList.put("Laser", new EnchantList("Laser", 4, 10000, "BeaconPoints", false, 6, true, false, false, 0));
        enchantList.put("Treasure Hunter", new EnchantList("Treasure Hunter", 10, 1000, "BeaconPoints", false, 6, true, false, false, 0));
        enchantList.put("Timer", new EnchantList("Timer", 5, 2500, "BeaconPoints", false, 6, true, false, false, 0));
        enchantList.put("Dragon Egg Finder", new EnchantList("Dragon Egg Finder", 1, 25000, "BeaconPoints", false, 6, false, true, false, 0));

        // (!) NELLA FUNZIONE CHECKPICK C'È UN CONTROLLO PER IL BEACON PICKAXE. SE DEVI AGGIUNGERE UN ENCHANT ALLORA GUARDA CHECKPICK() (!)
    }

    public Map<String, EnchantList> getEnchantList() {
        return enchantList;
    }

    public Map<String, EnchantList> getEnchanterEnchantList() {
        Map<String, EnchantList> enchanterEnchantList = new LinkedHashMap<>();
        for (String enchant : enchantList.keySet()) {
            EnchantList tmpEnchant = enchantList.get(enchant);
            if (tmpEnchant.isEnchanter()) {
                enchanterEnchantList.put(enchant, tmpEnchant);
            }
        }

        return enchanterEnchantList;
    }

    public List<String> getBeaconPickaxeEnchantList() {
        List<String> enchantList = new ArrayList<>();
        enchantList.add("Beacon Bomb");
        enchantList.add("Laser");
        enchantList.add("Treasure Hunter");
        enchantList.add("Timer");
        enchantList.add("Dragon Egg Finder");
        enchantList.add("Haste");
        enchantList.add("Beacon Miner");

        return enchantList;
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            if (event.getView().getTitle().equals("§e§lENCHANT §f| §7Menu")) {
                Player player = (Player) event.getWhoClicked();
                ItemStack clickedItem = event.getCurrentItem();
                event.setCancelled(true);

                if (event.getSlot() == 2) {
                    if (mainClass.hasPermissionFromLuckPerms(player, "effect.speed")) {
                        removePlayerEffect(player, "Speed");
                    } else {
                        givePlayerEffect(player, "Speed", getPlayerSpeedLevel(player));
                    }

                    openEnchantMenu(player);
                } else if (event.getSlot() == 6) {
                    if (mainClass.hasPermissionFromLuckPerms(player, "effect.haste")) {
                        removePlayerEffect(player, "Haste");
                    } else {
                        givePlayerEffect(player, "Haste", getPlayerHasteLevel(player));
                    }

                    openEnchantMenu(player);
                } else if (event.getSlot() == 4) {
                    if (mainClass.getPickaxeResetManager().canResetPick(player)) {
                        if (mainClass.getPickaxeResetManager().resetPick(player)) {
                            openEnchantMenu(player);
                        }
                    } else {
                        player.sendMessage(prefix + "§cYou have not all the requirements needed for Pick Reset");
                    }
                } else if (clickedItem != null && clickedItem.getType().equals(Material.FIREWORK_CHARGE)) {
                    String enchantName = bookManager.getEnchantName(clickedItem.getItemMeta().getDisplayName());
                    //AlphaBlockBreak.GetInstance().getLogger().info(enchantName);
                    //int maxEnchantLevel = enchantList.get(enchantName).getMaxLevel();
                    // NUOVA GESTIONE PER COMBO
                    int maxEnchantLevel = getEnchantMaxLevel(player, enchantName);
                    /*if (enchantList.get(enchantName).getBlocksNeededToUnlockNewLevels() > 0) {
                        PlayerData playerData = mainClass.getMongoReader().getPlayerData(player.getUniqueId());
                        int stepsReached = (playerData.getBlocksMined() == null ? 0 : playerData.getBlocksMined()) / enchantList.get(enchantName).getBlocksNeededToUnlockNewLevels();
                        maxEnchantLevel = maxEnchantLevel + (enchantList.get(enchantName).getLevelsToUnlock() * stepsReached);
                    }*/
                    long levelToAdd = 1;
                    boolean canEnchant = true;
                    if (event.getClick().equals(ClickType.SHIFT_LEFT)) {
                        String tmpEnchantName = enchantName.toLowerCase().replace(" ", "");
                        addRemoveMessagePermission(player, tmpEnchantName, enchantName);
                        canEnchant = false;
                    } else if (event.getClick().equals(ClickType.SHIFT_RIGHT)) {
                        String tmpEnchantName = enchantName.toLowerCase().replace(" ", "");
                        enableDisableEnchantPermission(player, tmpEnchantName, enchantName);
                        canEnchant = false;
                    } else if (event.getClick().isRightClick()) {
                        if (maxEnchantLevel > 1000 || enchantList.get(enchantName).isInfinite()) {
                            levelToAdd = 100;
                        } else {
                            levelToAdd = 10;
                        }
                    } else if (event.getAction().equals(InventoryAction.DROP_ONE_SLOT)) {
                        levelToAdd = 0;
                    }

                    if (canEnchant) {
                        if (player.getInventory().getItemInMainHand().hasItemMeta() && player.getInventory().getItemInMainHand().getItemMeta().hasLore()) {
                            enchant(player, enchantName, player.getInventory().getItemInMainHand().getItemMeta().getLore(), levelToAdd);
                        } else {
                            enchant(player, enchantName, new ArrayList<>(), levelToAdd);
                        }
                        openEnchantMenu(player);
                    }
                    /*ItemStack clickedSlot = getSlotItem(enchantName, player.getInventory().getItemInMainHand().getItemMeta().getLore(), player);
                    event.getClickedInventory().setItem(event.getSlot(), clickedSlot);*/
                }
            }
        }
    }

    public void addRemoveMessagePermission(Player player, String enchantName, String formattedName) {
        LuckPerms luckPerms = AlphaBlockBreak.GetInstance().getLuckPerms();
        User user = luckPerms.getPlayerAdapter(Player.class).getUser(player);
        Node node = Node.builder(enchantName + ".remove").build();
        if (player.hasPermission(enchantName + ".remove")) {
            user.data().remove(node);
            player.sendMessage(prefix + "Messages from " + formattedName + "§7 has been §aENABLED");
        } else {
            user.data().add(node);
            player.sendMessage(prefix + "Messages from " + formattedName + "§7 has been §cDISABLED");
        }

        luckPerms.getUserManager().saveUser(user);
    }

    public void enableDisableEnchantPermission(Player player, String enchantName, String formattedName) {
        LuckPerms luckPerms = AlphaBlockBreak.GetInstance().getLuckPerms();
        User user = luckPerms.getPlayerAdapter(Player.class).getUser(player);
        Node node = Node.builder(enchantName + ".disable").build();
        if (player.hasPermission(enchantName + ".disable")) {
            user.data().remove(node);
            player.sendMessage(prefix + formattedName + "§7 has been §aENABLED");
        } else {
            user.data().add(node);
            player.sendMessage(prefix + formattedName + "§7 has been §cDISABLED");
        }

        luckPerms.getUserManager().saveUser(user);
    }

    public boolean enchantExists(String enchantName) {
        return enchantList.containsKey(enchantName);
    }

    public double[] getPrice(Player player, String enchantName, long currentLevel, double levelToAdd, double playerTokens) {
        double singleLevelPrice = enchantList.get(enchantName).getPrice();
        PlayerData playerData = mainClass.getMongoReader().getPlayerData(player.getUniqueId());

        if (playerData.getPickaxeResetAmount() != null) {
            // Escludo i dungeon enchants
            List<String> excludedEnchants = Arrays.asList("Dungeon Fortune", "Dungeon Luck", "Dungeon Killer", "Dungeon Bomb", "Lucky Beacon", "Dungeon Finder");
            if (!excludedEnchants.contains(enchantName)) {
                singleLevelPrice = singleLevelPrice + (singleLevelPrice * (0.1 * playerData.getPickaxeResetAmount()));
            }
        }

        double[] data = new double[2];
        int maxLevelEnchant = getEnchantMaxLevel(player, enchantName);
        boolean isInfinite = enchantList.get(enchantName).isInfinite();

        double finalPrice = 0;
        double actualLevelsToAdd = levelToAdd;

        if (levelToAdd == 0) {
            // MAX ENCHANT - Calculate maximum levels player can afford
            actualLevelsToAdd = calculateMaxAffordableLevels(enchantName, currentLevel, singleLevelPrice, playerTokens, maxLevelEnchant, isInfinite);
            finalPrice = calculateTotalPrice(enchantName, currentLevel, actualLevelsToAdd, singleLevelPrice);
        } else if (levelToAdd > 1) {
            // Multiple levels
            if (!isInfinite && currentLevel + levelToAdd > maxLevelEnchant) {
                actualLevelsToAdd = Math.max(0, maxLevelEnchant - currentLevel);
            }
            finalPrice = calculateTotalPrice(enchantName, currentLevel, actualLevelsToAdd, singleLevelPrice);
        } else {
            // Single level
            if (!isInfinite && currentLevel >= maxLevelEnchant) {
                actualLevelsToAdd = Math.max(0, maxLevelEnchant - currentLevel);
            }
            finalPrice = calculateTotalPrice(enchantName, currentLevel, actualLevelsToAdd, singleLevelPrice);
        }

        // Apply discounts
        if (player.hasPermission("group.alpha+")) {
            finalPrice = finalPrice * 0.9; // 10% discount
        }

        // Apply enchant discount from Discount Skill books
        PlayerData playerData = mainClass.getMongoReader().getPlayerData(player.getUniqueId());
        int enchantDiscount = playerData.getEnchantDiscount();
        if (enchantDiscount > 0) {
            double discountMultiplier = (100.0 - enchantDiscount) / 100.0;
            finalPrice = finalPrice * discountMultiplier;
        }

        data[0] = finalPrice;
        data[1] = actualLevelsToAdd;
        return data;
    }

    /**
     * Calculate maximum levels player can afford using mathematical formula instead of loops
     */
    private double calculateMaxAffordableLevels(String enchantName, long currentLevel, double singleLevelPrice, double playerTokens, int maxLevel, boolean isInfinite) {
        if (playerTokens <= 0) return 0;

        // Check if this is an infinite enchant with exponential scaling
        if (isInfinite && enchantList.get(enchantName).getGrowthRate() > 0) {
            return calculateMaxAffordableLevelsExponential(enchantName, currentLevel, singleLevelPrice, playerTokens);
        }

        // For arithmetic progression: Sum = n/2 * (2a + (n-1)d)
        // Where: a = first term, d = common difference, n = number of terms
        // Rearranging to solve for n: n = (-2a + sqrt(4a² + 8dS)) / (2d)

        double firstPrice = (currentLevel + 1) * singleLevelPrice;

        // Use binary search for very large numbers to avoid floating point precision issues
        /*if (currentLevel > 1000000) {
            return binarySearchMaxLevels(enchantName, currentLevel, singleLevelPrice, playerTokens, maxLevel, isInfinite);
        }*/

        // Mathematical solution for smaller numbers
        double discriminant = firstPrice * firstPrice + 2 * singleLevelPrice * playerTokens;
        if (discriminant < 0) return 0;

        double maxAffordable = (-firstPrice + Math.sqrt(discriminant)) / singleLevelPrice;

        if (!isInfinite) {
            maxAffordable = Math.min(maxAffordable, maxLevel - currentLevel);
        }

        return Math.max(0, Math.floor(maxAffordable));
    }

    /**
     * Calculate maximum affordable levels for exponential scaling enchants
     */
    private double calculateMaxAffordableLevelsExponential(String enchantName, long currentLevel, double basePrice, double playerTokens) {
        double growthRate = enchantList.get(enchantName).getGrowthRate();
        if (growthRate <= 0) return 0;

        double ratio = 1 + growthRate;
        double firstTerm = basePrice * Math.pow(ratio, currentLevel);

        // Solve: budget = firstTerm * (ratio^n - 1) / (ratio - 1)
        // Rearranging: n = log(1 + (budget * (ratio-1)) / firstTerm) / log(ratio)

        if (firstTerm <= 0) return 0;

        double argument = 1 + (playerTokens * (ratio - 1)) / firstTerm;
        if (argument <= 1) return 0;

        double maxLevels = Math.log(argument) / Math.log(ratio);
        return Math.max(0, Math.floor(maxLevels));
    }

    /**
     * Binary search approach for very high levels to avoid precision issues
     */
    private double binarySearchMaxLevels(String enchantName, long currentLevel, double singleLevelPrice, double playerTokens, int maxLevel, boolean isInfinite) {
        long low = 0;
        long high = isInfinite ? (long)(playerTokens / singleLevelPrice) + 1000 : maxLevel - currentLevel;

        while (low < high) {
            long mid = low + (high - low + 1) / 2;
            double cost = calculateTotalPrice(enchantName, currentLevel, mid, singleLevelPrice);

            if (cost <= playerTokens) {
                low = mid;
            } else {
                high = mid - 1;
            }
        }

        return low;
    }

    /**
     * Calculate total price using arithmetic sequence formula: Sum = n/2 * (first + last)
     * For infinite enchants with growth rate > 0, uses exponential scaling
     */
    private double calculateTotalPrice(String enchantName, long currentLevel, double levelsToAdd, double singleLevelPrice) {
        if (levelsToAdd <= 0) return 0;

        // Check if this is an infinite enchant with exponential scaling
        boolean isInfinite = enchantList.get(enchantName).isInfinite();
        double growthRate = enchantList.get(enchantName).getGrowthRate();

        if (isInfinite && growthRate > 0) {
            return calculateTotalPriceExponential(enchantName, currentLevel, levelsToAdd, singleLevelPrice);
        }

        // Linear pricing for finite enchants or infinite enchants with growthRate = 0
        double firstPrice = (currentLevel + 1) * singleLevelPrice;
        double lastPrice = (currentLevel + levelsToAdd) * singleLevelPrice;

        return (levelsToAdd / 2.0) * (firstPrice + lastPrice);
    }

    /**
     * Calculate total price for exponential scaling enchants using geometric series
     */
    private double calculateTotalPriceExponential(String enchantName, long currentLevel, double levelsToAdd, double basePrice) {
        double growthRate = enchantList.get(enchantName).getGrowthRate();
        if (growthRate <= 0) return 0;

        double ratio = 1 + growthRate;
        double firstTerm = basePrice * Math.pow(ratio, currentLevel);

        // Sum of geometric series: a * (r^n - 1) / (r - 1)
        // where a = firstTerm, r = ratio, n = levelsToAdd
        double totalCost = firstTerm * (Math.pow(ratio, levelsToAdd) - 1) / (ratio - 1);

        return totalCost;
    }

    /**
     * Alternative method using direct arithmetic progression formula
     */
    private double calculateTotalPriceAlternative(long currentLevel, double levelsToAdd, double singleLevelPrice) {
        if (levelsToAdd <= 0) return 0;

        // Sum of arithmetic progression: n/2 * (2a + (n-1)d)
        // where a = (currentLevel + 1) * singleLevelPrice, d = singleLevelPrice, n = levelsToAdd
        double firstTerm = (currentLevel + 1) * singleLevelPrice;
        return (levelsToAdd / 2.0) * (2 * firstTerm + (levelsToAdd - 1) * singleLevelPrice);
    }

    public double getPriceForLevel(double singleLevelPrice, double levels) {
        return levels * (levels + 1) / 2 * singleLevelPrice;
    }

    private boolean isExactName(String name) {
        int len = name.length();
        for (int i = 0; i < len; i++) {
            if (Character.isLetter(name.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    private long getCurrentLevelFromLore(List<String> lore, String enchantName) {
        long currentLevel = 0;

        String tmp;
        for (String line : lore) {
            if (line.contains(getChatColor(enchantName) + "▍ §7" + enchantName + " ")) {
                tmp = line.replace(getChatColor(enchantName) + "▍ §7" + enchantName + " ", "");
                if (isExactName(tmp)) {
                    tmp = ChatColor.stripColor(tmp);
                    currentLevel = Long.parseLong(tmp);
                }
            }
        }

        return currentLevel;
    }

    public long getCurrentLevel(List<String> lore, String enchantName) {
        long currentLevel = 0;

        if (lore != null && lore.size() > 0) {
            //int hiddenStringIndex = -1;
            //for (int i = 0; i < lore.size(); i++) {
            //    //AlphaBlockBreak.GetInstance().getLogger().info("index: " + i + " - " + lore.get(i) + " - " + HiddenStringUtils.extractHiddenString(lore.get(i)));
            //    if (HiddenStringUtils.hasHiddenString(lore.get(i))) {
            //        hiddenStringIndex = i;
            //    }
            //}
            /*if (HiddenStringUtils.hasHiddenString(lore.get(lore.size() - 1))) {
                String jsonString = HiddenStringUtils.extractHiddenString(lore.get(lore.size() - 1));
                JsonObject object = new JsonParser().parse(jsonString).getAsJsonObject();
                if (object.get(enchantName) != null) {
                    currentLevel = object.get(enchantName).getAsLong();
                } else {
                    currentLevel = getCurrentLevelFromLore(lore, enchantName);
                }
            } else {
                currentLevel = getCurrentLevelFromLore(lore, enchantName);
            }*/
            currentLevel = getCurrentLevelFromLore(lore, enchantName);
        }

        return currentLevel;
    }

    public Color getColor(String enchantName) {
        int color = enchantList.get(enchantName).getColor();
        if (color == 1) {
            return Color.LIME;
        } else if (color == 2) {
            return Color.YELLOW;
        } else if (color == 3) {
            return Color.ORANGE;
        } else if (color == 4) {
            return Color.RED;
        } else if (color == 5) {
            return Color.BLACK;
        } else {
            return Color.AQUA;
        }
    }

    public String getChatColor(String enchantName) {
        int color = enchantList.get(enchantName).getColor();
        if (color == 1) {
            return "§a";
        } else if (color == 2) {
            return "§e";
        } else if (color == 3) {
            return "§6";
        } else if (color == 4) {
            return "§c";
        } else if (color == 5){
            return "§8";
        } else {
            return "§b";
        }
    }

    private List<String> getSlotLore(String enchantName, List<String> pickaxeLore, Player player, boolean price) {
        List<String> lore = new ArrayList<>();
        long currentLevel = getCurrentLevel(pickaxeLore, enchantName);

        if (enchantList.get(enchantName).isNew()) {
            lore.add("§c§l* NEW *");
            lore.add("");
        } else if (enchantList.get(enchantName).isUnique()) {
            lore.add("§c§l* UNIQUE *");
            lore.add("");
        }
        //AlphaBlockBreak.GetInstance().getLogger().info(enchantName);
        switch (enchantName) {
            case "Efficiency":
                lore.add("§7§o(( Increases the speed of which you");
                lore.add("§7§obreak a block ))");
                break;
            case "Unbreaking":
                lore.add("§7§o(( Your pickaxe will last longer ))");
                break;
            case "Fortune":
                lore.add("§7§o(( Increases the amount of items");
                lore.add("§7§oyou get per block ))");
                break;
            case "Speed":
                lore.add("§7§o(( Gives you a movement speed boost");
                lore.add("§7§owhile holding your pick ))");
                break;
            case "Haste":
                lore.add("§7§o(( Make you mine faster ))");
                break;
            case "Greed":
                lore.add("§7§o(( Make you sell items for");
                lore.add("§7§omore money ))");
                break;
            case "Token Greed":
                lore.add("§7§o(( Make you find larger amount");
                lore.add("§7§oof tokens while mining ))");
                break;
            case "Key Finder":
                lore.add("§7§o(( Increases the amount of keys");
                lore.add("§7§oyou get from mining ))");
                break;
            case "Combo":
                lore.add("§7§o(( Gives you a chance of receiving");
                lore.add("§7§odouble tokens from Token Greed ))");
                break;
            case "XPGreed":
                lore.add("§7§o(( Each level will give you more XP");
                lore.add("§7§ofor each block broken ))");
                break;
            case "Block Miner":
                lore.add("§7§o(( You will have a chance to get");
                lore.add("§7§o1 extra block for blocktop payouts ))");
                break;
            case "Randomizer":
                lore.add("§7§o(( Allow you to find random stuff while");
                lore.add("§7§omining like Money, Tokens or Keys ))");
                break;
            case "RandomizerMerchant":
                lore.add("§7§o(( Each level gives you a better chance to");
                lore.add("§7§oget double rewards from Randomizer Enchant ))");
                break;
            case "LuckyBlock":
                lore.add("§7§o(( Allows you to mine LuckyBlocks ))");
                break;
            case "LuckyKey":
                lore.add("§7§o(( Every 5 levels you will be able to get");
                lore.add("§7§oa chance to receive more keys from Randomizer Enchant ))");
                break;
            case "BeaconGreed":
                lore.add("§7§o(( Every 5 levels you will gain 1 Extra Beacon ))");
                break;
            case "Prestige Point Finder":
                lore.add("§7§o(( You will receive 1 Point for /PrestigeShop ))");
                lore.add("§7§o( -10 Blocks every level, starting from 50 )");
                break;
            case "Voucher Finder":
                lore.add("§7§o(( Allows you to get Autosell, Tokengreed");
                lore.add("§7§oand Nuke boosters for a certain amount of time ))");
                break;
            case "Prestige Finder":
                lore.add("§7§o(( Each level gives you a better chance");
                lore.add("§7§oto find a Prestige Level while mining ))");
                break;
            case "Robot Finder":
                lore.add("§7§o(( Makes you find robots for /Robot while mining ))");
                break;
            case "Blessing":
                lore.add("§7§o(( You will have a chance to give");
                lore.add("§7§oto everyone FREE Tokens ))");
                break;
            case "JackPot":
                lore.add("§7§o(( You will get a 1.2x TokenGreed boost.");
                lore.add("§7§oEvery extra level will add another 0.05x ))");
                break;
            case "Nuke":
                lore.add("§7§o(( Gives you a chance to destroy the whole mine and give you");
                lore.add("§7§oMoney (based on pickaxe fortune level)");
                lore.add("§7§oand Tokens (based on prestige level)");
                lore.add("§7§oMoney and Token boosters from keys affect the rewards");
                lore.add("§7§oby 1/5 of the boosters value. ))");
                break;
            case "JackHammer":
                lore.add("§7§o(( Gives you a chance to destroy a layer of the mine");
                lore.add("§7§oand give you money as a reward ))");
                break;
            case "Spawner Shard Finder":
                lore.add("§7§o(( You will have a chance to get");
                lore.add("§7§oSpawner Shards for /SpawnerShop ))");
                break;
            case "WonderLand":
                lore.add("§7§o(( Transforms your mine into a Wonderland");
                lore.add("§7§owith a 25x Money and Token Boost for 5 minutes.");
                lore.add("§7§oCooldown of 55 minutes ))");
                break;
            case "Timelapse":
                lore.add("§7§o(( This enchant allows you to farm for 1 minute");
                lore.add("§7§oand then boost the rewards you earn in that minute ))");
                break;
            case "Fracture":
                lore.add("§7§o(( This special enchant will start an earthquake");
                lore.add("§7§owhich will make blocks ascend into the sky");
                lore.add("§7§ogiving you insane rewards ))");
                break;
            case "Fire Road":
                lore.add("§7§o(( This enchant will let you change the blocks");
                lore.add("§7§oaround you into diabolic blocks.");
                lore.add("§7§oThe value of this block were chosen by the devil himself ))");
                break;
            case "Explosive":
                lore.add("§7§o(( You will have a chance to mine");
                lore.add("§7§odouble blocks and obtain double rewards ))");
                break;
            case "FriendShip":
                lore.add("§7§o(( Will give random Keys");
                lore.add("§7§oto all your island members ))");
                break;
            case "Candy Finder":
                lore.add("§7§o(( Christmas unique enchant");
                lore.add("§7§othat gives you Candy while mining ))");
                break;
            case "ArmorPoint Finder":
                lore.add("§7§o(( Will give you points");
                lore.add("§7§oto upgrade your /Armor ))");
                break;
            case "Dungeon Fortune":
                lore.add("§7§o(( You will have a chance to");
                lore.add("§7§odouble the crystals from the ores ))");
                break;
            case "Dungeon Killer":
                lore.add("§7§o(( Every minute all the mobs");
                lore.add("§7§oaround you will be killed by your aura ))");
                break;
            case "Dungeon Luck":
                lore.add("§7§o(( You will have a chance to spawn");
                lore.add("§7§oa better ore while mining ))");
                break;
            case "Dungeon Bomb":
                lore.add("§7§o(( Every stone block in a range of 5 blocks");
                lore.add("§7§owill be turned into a random ore ))");
                break;
            case "Dungeon Finder":
                lore.add("§7§o(( Discover mystical ores while mining");
                lore.add("§7§oin the dungeon. Higher levels increase");
                lore.add("§7§oyour chances of finding rare materials ))");
                break;
            case "Beacon Bomb":
                lore.add("§7§o(( Every level will give you an higher");
                lore.add("§7§ochance to drop a bomb in the beacon mine ))");
                break;
            case "Laser":
                lore.add("§7§o(( You will have a chance to destroy all the beacons");
                lore.add("§7§oin a certain direction ))");
                break;
            case "Treasure Hunter":
                lore.add("§7§o(( You will receive a random amount of beacons");
                lore.add("§7§ofrom 1 to 64 while mining ))");
                break;
            case "Timer":
                lore.add("§7§o(( Every 1.5 minutes you will have access to the");
                lore.add("§7§oSUPER HASTE effect. Each level gives you extra time ))");
                break;
            case "Dragon Egg Finder":
                lore.add("§7§o(( While mining beacons you will receive");
                lore.add("§7§oDragon Egg used for the island level ))");
                break;
            case "Lucky Beacon":
                lore.add("§7§o(( Every level will give you 1 extra");
                lore.add("§7§obeacon from LuckyBlocks ))");
                break;
            case "ValueFinder":
                lore.add("§7§o(( Every level will increase the chance");
                lore.add("§7§oto find some island value.");
                lore.add("§7§oValue is added directly to your island ))");
                break;
            case "Meteor":
                lore.add("§7§o(( Create spherical explosion in the mine");
                lore.add("§7§oand apply Fortune and TokenGreed on the exploded blocks.");
                lore.add("§7§oIncrease level to increase the proc rate ))");
                break;
        }

        lore.add("");
        int tierNeeded = enchantList.get(enchantName).getColor() - 1;
        if (enchantList.get(enchantName).getColor() == 6) {
            tierNeeded = 0;
        }
        PlayerData data = mainClass.getMongoReader().getPlayerData(player.getUniqueId());

        int currentTier = data.getPickaxeCurrentTier() == null ? 0 : data.getPickaxeCurrentTier();
        String tmpEnchantName = enchantName.toLowerCase().replace(" ", "");
        if (currentTier >= tierNeeded || player.isOp()) {
            lore.add("§e§lLEVELS");
            lore.add("§e| §fCurrent: §e" + currentLevel);
            int maxLevel = getEnchantMaxLevel(player, enchantName);
            if (maxLevel > 0) {
                if (enchantList.get(enchantName).getLevelsToUnlockPerReset() > 0) {
                    lore.add("§e| §fCurrent Max level: §e" + maxLevel);
                    lore.add("§e| §fUnlock §e" + enchantList.get(enchantName).getLevelsToUnlockPerReset() + "§f levels every §ePick Reset");
                } else {
                    lore.add("§e| §fMax: §e" + enchantList.get(enchantName).getMaxLevel());
                    lore.add("§e| §fMax level doesn't change with Pick Reset");
                }
            } else {
                lore.add("§e| §fMax: §eထ");
            }

            /*// Add drop rate information for Dungeon Finder
            if (enchantName.equals("Dungeon Finder") && currentLevel > 0) {
                lore.add("");
                lore.add("§6§lCUSTOM ORES §7(Current Level):");
                lore.add("§7• §bEnergium: §a" + String.format("%.2f", CustomOre.ENERGIUM.getDropRateForLevel((int) currentLevel)) + "%");
                lore.add("§7• §dTemporium: §a" + String.format("%.2f", CustomOre.TEMPORIUM.getDropRateForLevel((int) currentLevel)) + "%");
                lore.add("§7• §5Mystrium: §a" + String.format("%.2f", CustomOre.MYSTRIUM.getDropRateForLevel((int) currentLevel)) + "%");
                lore.add("§7• §8Voidstone: §a" + String.format("%.2f", CustomOre.VOIDSTONE.getDropRateForLevel((int) currentLevel)) + "%");
                lore.add("§7• §fPrismatic: §a" + String.format("%.2f", CustomOre.PRISMATIC.getDropRateForLevel((int) currentLevel)) + "%");
            }*/

            if (price) {
                if (currentLevel != 0 && currentLevel == maxLevel) {
                    lore.add("§e| §aMax Level Reached");
                } else {
                    // Special pricing display for Dungeon Finder
                    if (enchantName.equals("Dungeon Finder")) {
                        lore.add("§e| §fPrice: §e5,000 §fT1 + §e2,500 §fT2 + §e1,250 §fT3 Crystals");
                        lore.add("§e| §7(Per Level - Multi-Tier Required)");
                    } else if (enchantList.get(enchantName).getTier() > 0) {
                        lore.add("§e| §fPrice: §e" + mainClass.newFormatNumber(getPrice(player, enchantName, currentLevel, 1, 9999999999999L)[0], player) + " §fTier " + enchantList.get(enchantName).getTier() + " Crystals");
                    } else {
                        lore.add("§e| §fPrice: §f⛂ §e" + mainClass.newFormatNumber(getPrice(player, enchantName, currentLevel, 1, 9999999999999L)[0], player) + " §f" + enchantList.get(enchantName).getValuta());
                    }

                    if (enchantList.get(enchantName).getBlocksNeeded() > 0 && currentLevel > 0) {
                        lore.add("");
                        lore.add("§a§lEXTRA REQUIREMENTS");
                        lore.add("§a| §fBlocks Needed: §a" + mainClass.newFormatNumber(enchantList.get(enchantName).getBlocksNeeded() * currentLevel, player));
                    }

                    lore.add("");
                    lore.add("§6§lUSAGE");
                    lore.add("§6| §fLeft Click: §6+1 §fLevel");
                    if (maxLevel > 1000 || enchantList.get(enchantName).isInfinite()) {
                        lore.add("§6| §fRight Click: §6+100 §fLevels");
                    } else {
                        lore.add("§6| §fRight Click: §6+10 §fLevels");
                    }
                    lore.add("§6| §fQ: Max Level");
                }

                lore.add("");
                lore.add("§f§lEXTRA");
                if (!player.hasPermission(tmpEnchantName + ".remove")) {
                    lore.add("§f| Shift + Left Click: Disable Message");
                } else {
                    lore.add("§f| Shift + Left Click: Enable Message");
                }
                if (!player.hasPermission(tmpEnchantName + ".disable")) {
                    lore.add("§f| Shift + Right Click: Disable Enchant");
                } else {
                    lore.add("§f| Shift + Right Click: Enable Enchant");
                }
                lore.add("");
            } else {
                lore.add("");
            }
        } else {
            lore.add("§c§lLOCKED");
            lore.add("§c| §fYou need to reach tier §l" + tierNeeded);
            lore.add("§c| §fto unlock and access this enchant.");
            lore.add("§c| §fTo get an higher tier you need to mine blocks");
            lore.add("");
            if (!player.hasPermission(tmpEnchantName + ".remove")) {
                lore.add("§f| Shift + Left Click: Disable Message");
            } else {
                lore.add("§f| Shift + Left Click: Enable Message");
            }
            if (!player.hasPermission(tmpEnchantName + ".disable")) {
                lore.add("§f| Shift + Right Click: Disable Enchant");
            } else {
                lore.add("§f| Shift + Right Click: Enable Enchant");
            }
            lore.add("");
        }

        return lore;
    }

    private ItemMeta addGlowing(ItemMeta meta) {
        meta.addEnchant(Enchantment.DAMAGE_ALL, 1, true);
        meta.addItemFlags(ItemFlag.HIDE_ENCHANTS);

        return meta;
    }

    public ItemStack getSlotItem(String enchantName, List<String> pickaxeLore, Player player) {
        return getSlotItemv2(enchantName, pickaxeLore, player, true);
    }

    public ItemStack getSlotItemv2(String enchantName, List<String> pickaxeLore, Player player, boolean price) {
        ItemStack enchantItem = new ItemStack(Material.FIREWORK_CHARGE);
        FireworkEffectMeta fireworksMeta = (FireworkEffectMeta) enchantItem.getItemMeta();
        FireworkEffect.Builder fireworksEffect = FireworkEffect.builder();

        Color enchantColor = getColor(enchantName);

        fireworksEffect.withColor(enchantColor);
        fireworksEffect.flicker(false);
        fireworksEffect.trail(false);
        fireworksEffect.withFade(enchantColor);
        fireworksMeta.setEffect(fireworksEffect.build());

        enchantItem.setItemMeta(fireworksMeta);
        ItemMeta meta = enchantItem.getItemMeta();
        meta.setDisplayName(getChatColor(enchantName) + enchantName);
        meta.setLore(getSlotLore(enchantName, pickaxeLore, player, price));
        meta.addItemFlags(ItemFlag.HIDE_POTION_EFFECTS);

        long currentLevel = getCurrentLevel(pickaxeLore, enchantName);
        //int maxLevel = enchantList.get(enchantName).getMaxLevel();
        // NUOVA GESTIONE PER COMBO
        int maxLevel = getEnchantMaxLevel(player, enchantName);
        /*if (enchantList.get(enchantName).getBlocksNeededToUnlockNewLevels() != -1) {
            PlayerData playerData = mainClass.getMongoReader().getPlayerData(player.getUniqueId());
            int stepsReached = (playerData.getBlocksMined() == null ? 0 : playerData.getBlocksMined()) / enchantList.get(enchantName).getBlocksNeededToUnlockNewLevels();
            maxLevel = maxLevel + (enchantList.get(enchantName).getLevelsToUnlock() * stepsReached);
        }*/
        if (currentLevel != 0 && currentLevel >= maxLevel && !enchantList.get(enchantName).isInfinite()) {
            addGlowing(meta);
        }
        //meta.addItemFlags(ItemFlag.HIDE_ATTRIBUTES);
        enchantItem.setItemMeta(meta);

        return enchantItem;
    }

    // Funzione che rimuove il vecchio Randomizer Merchant
    public void checkPick(Player player) {
        ItemStack itemInHand = player.getInventory().getItemInMainHand();
        boolean isBeaconPick = mainClass.isBeaconPickaxe(player);
        List<String> lore = itemInHand.getItemMeta().getLore();
        List<String> newLore = new ArrayList<>();

        if (lore != null && !lore.isEmpty()) {
            for (String line : lore) {
                // GUARDA QUA PER BEACON PICK
                if (!line.contains("Randomizer Merchant ")) {
                    if (!isBeaconPick) {
                        newLore.add(line);
                    } else {
                        // SE È BEACON PICK CONTROLLO DI INSERIRE SOLO LE RIGHE DEGLI ENCHANT GIUSTI
                        if (line.contains("Haste") || line.contains("Beacon Bomb") || line.contains("Laser") || line.contains("Treasure Hunter") || line.contains("Beacon Miner") || line.contains("Dragon Egg Finder")) {
                            newLore.add(line);
                        }
                    }
                }
            }

            ItemMeta meta = itemInHand.getItemMeta();
            meta.setLore(newLore);
            itemInHand.setItemMeta(meta);
            player.updateInventory();
        }
    }

    public static boolean isNumeric(String strNum) {
        if (strNum == null) {
            return false;
        }
        try {
            double d = Double.parseDouble(strNum);
        } catch (NumberFormatException nfe) {
            return false;
        }
        return true;
    }

    public List<String> orderLore(List<String> lore) {
        List<String> tmpLore = new ArrayList<>();

        int hiddeLoreIndex = -1;
        for (int i = 0; i < lore.size(); i++) {
            if (HiddenStringUtils.hasHiddenString(lore.get(i))) {
                hiddeLoreIndex = i;
            }
        }
        if (hiddeLoreIndex != -1) {
            for (int i = 0; i < lore.size(); i++) {
                if (i != hiddeLoreIndex) {
                    tmpLore.add(lore.get(i));
                }
            }
            tmpLore.add(lore.get(hiddeLoreIndex));
        } else {
            tmpLore = lore;
        }

        return tmpLore;
    }

    public List<String> checkLevelFormatted(List<String> lore) {
        List<String> tmpLore = new ArrayList<>();
        String jsonString = HiddenStringUtils.extractHiddenString(lore.get(lore.size() - 1));
        JsonObject object = new JsonParser().parse(jsonString).getAsJsonObject();

        for (int i = 0; i < lore.size(); i++) {
            String[] splitted = lore.get(i).split(" ");
            String enchantLevel = splitted[splitted.length - 1];
            if (isNumeric(enchantLevel) && Long.parseLong(enchantLevel) > 999) {
                StringBuilder enchantName = new StringBuilder();
                if (splitted.length > 2) {
                    for (int j = 0; j < splitted.length; j++) {
                        if (j > 0 && j < splitted.length - 1) {
                            enchantName.append(splitted[j]);
                            if (j < splitted.length - 2) {
                                enchantName.append(" ");
                            }
                        }
                    }
                } else {
                    enchantName.append(splitted[1]);
                }
                String finalEnchantName = "";
                finalEnchantName = bookManager.getEnchantName(enchantName.toString());
                object.addProperty(finalEnchantName, mainClass.newFormatNumber(Long.parseLong(enchantLevel)));
                HiddenStringUtils.replaceHiddenString(lore.get(lore.size() - 1), object.toString());
                tmpLore.add(getChatColor(finalEnchantName) + "▍ §7" + finalEnchantName + " " + mainClass.newFormatNumber(Long.parseLong(enchantLevel)));
            } else if (!HiddenStringUtils.hasHiddenString(lore.get(i))) {
                tmpLore.add(lore.get(i));
            }
        }
        tmpLore.add(HiddenStringUtils.encodeString(object.toString()));

        return tmpLore;
    }

    public void addEnchant(Player player, String enchantName, int levels, boolean sendMessage) {
        ItemStack itemInHand = player.getInventory().getItemInMainHand();
        enchantName = bookManager.getEnchantName(enchantName);

        if (itemInHand.getType().equals(XMaterial.DIAMOND_PICKAXE.parseMaterial())) {

            List<String> lore = player.getInventory().getItemInMainHand().getItemMeta().getLore() != null ? player.getInventory().getItemInMainHand().getItemMeta().getLore() : new ArrayList<>();
            List<String> newLore = new ArrayList<>();

            long currentLevel = getCurrentLevel(lore, enchantName);

            boolean enchantFinded = false;
            if (lore != null) {
                for (String line : lore) {
                    if (line.contains(getChatColor(enchantName) + "▍ §7" + enchantName + " ") && !line.contains(" Merchant ")) {
                        line = line.replace(getChatColor(enchantName) + "▍ §7" + enchantName + " " + currentLevel, getChatColor(enchantName) + "▍ §7" + enchantName + " " + (currentLevel + levels));
                        enchantFinded = true;
                    }
                    if (!HiddenStringUtils.hasHiddenString(line)) {
                        newLore.add(line);
                    }
                }
            }
            if (!enchantFinded) {
                newLore.add(getChatColor(enchantName) + "▍ §7" + enchantName + " " + levels);
            }

            if (sendMessage) {
                player.sendMessage(prefix + "You received §a§l" + levels + " §7level/s of §a§l" + enchantName);
            }
            ItemMeta meta = itemInHand.getItemMeta();
            meta.setLore(newLore);
            addGlowing(meta);
            itemInHand.setItemMeta(meta);
            player.updateInventory();
        } else {
            player.sendMessage(prefix + "§cYou must hold your pickaxe to obtain levels");
        }
    }

    public void sortEnchant(Player player) {
        ItemStack itemInHand = player.getInventory().getItemInMainHand();
        ItemMeta meta = itemInHand.getItemMeta();
        List<String> itemLore = itemInHand.getItemMeta().getLore();
        List<String> newLore = new ArrayList<>();
        List<String> initialLore = new ArrayList<>();

        for (String enchant : enchantList.keySet()) {
            enchant = "§7" + enchant;
            //player.sendMessage(enchant);
            //if (itemLore.contains(enchant)) {
            //    player.sendMessage("OK");
                for (String line : itemLore) {
                    if (line.contains(enchant)) {
                        newLore.add(line);
                        break;
                    }
                }
            //}
        }

        // 26/03/2022 AGGIUNGO TUTTE LE RIGHE CHE NON SONO ENCHANT ALLA FINE ( TIPO BEACON MINER )
        for (String line : itemLore) {
            if (!newLore.contains(line)) {
                initialLore.add(line);
            }
        }
        initialLore.addAll(newLore);

        meta.setLore(initialLore);
        itemInHand.setItemMeta(meta);
    }

    public void enchant(Player player, String enchantName, List<String> lore, long levelToAdd) {
        ItemStack itemInHand = player.getInventory().getItemInMainHand();
        AlphaBlockBreak main = AlphaBlockBreak.GetInstance();
        TokenEnchantAPI teAPI = main.getTeAPI();

        if (itemInHand.getType().equals(XMaterial.DIAMOND_PICKAXE.parseMaterial()) || (itemInHand.getType().equals(XMaterial.DIAMOND_AXE.parseMaterial()) && enchantName.equalsIgnoreCase("Efficiency"))) {
            List<String> newLore = new ArrayList<>();
            enchantName = bookManager.getEnchantName(enchantName);

            // 18/12/2021 AGGIUNTO CONTROLLO PER EVITARE DI BUGGARE RANDOMIZER
            if (enchantName.equalsIgnoreCase("RandomizerMerchant")) {
                long randomizerLevel = getCurrentLevel(lore, "Randomizer");
                if (randomizerLevel == 0) {
                    player.sendMessage(prefix + "§cYou must buy Randomizer before the Randomizer Merchant !");
                    return;
                }
            }

            // uso color come tier ( color - 1 perchè inizia da 1 )
            int tierNeeded = enchantList.get(enchantName).getColor() - 1;
            if (enchantList.get(enchantName).getColor() == 6) {
                tierNeeded = 0;
            }
            PlayerData data = main.getMongoReader().getPlayerData(player.getUniqueId());

            int currentTier = data.getPickaxeCurrentTier() == null ? 0 : data.getPickaxeCurrentTier();
            int currentPickReset = data.getPickaxeResetAmount() == null ? 0 : data.getPickaxeResetAmount();
            if (currentTier >= tierNeeded || player.isOp()) {
                //if (currentPickReset >= enchantList.get(enchantName).getPickResetRequired() || player.isOp()) {
                    double playerTokens = teAPI.getTokens(player);
                    int enchantTier = enchantList.get(enchantName).getTier();
                    String valuta = enchantList.get(enchantName).getValuta();
                    long playerCrystals = main.getDungeonSystem().getPoints(player, enchantTier);
                    long playerBeaconpoints = main.getBeaconPointSystem().getPoints(player);
                    int playerBlocks = main.getPlayerBlocks(player);

                    int blocksNeeded = enchantList.get(enchantName).getBlocksNeeded();

                    long currentLevel = getCurrentLevel(lore, enchantName);
                    double[] getEnchantData = getPrice(player, enchantName, currentLevel, levelToAdd, playerTokens);
                    double price = getEnchantData[0];
                    levelToAdd = (long) getEnchantData[1];

                    // Custom pricing for Dungeon Finder enchant
                    if (enchantName.equals("Dungeon Finder")) {
                        // Calculate multi-tier pricing: 5000 tier 1 + 2500 tier 2 + 1250 tier 3 per level
                        long tier1Cost = 5000L * levelToAdd;
                        long tier2Cost = 2500L * levelToAdd;
                        long tier3Cost = 1250L * levelToAdd;

                        long playerTier1 = main.getDungeonSystem().getPoints(player, 1);
                        long playerTier2 = main.getDungeonSystem().getPoints(player, 2);
                        long playerTier3 = main.getDungeonSystem().getPoints(player, 3);

                        // Check if player can afford all tiers
                        if (playerTier1 >= tier1Cost && playerTier2 >= tier2Cost && playerTier3 >= tier3Cost) {
                            // Deduct from all tiers
                            main.getDungeonSystem().removePoints(player, 1, tier1Cost);
                            main.getDungeonSystem().removePoints(player, 2, tier2Cost);
                            main.getDungeonSystem().removePoints(player, 3, tier3Cost);

                            // Apply the enchant
                            boolean enchantFinded = false;
                            List<String> tmpLore = new ArrayList<>();
                            if (lore != null) {
                                for (String line : lore) {
                                    if (line.contains(getChatColor(enchantName) + "▍ §7" + enchantName + " ") && !line.contains(" Merchant ")) {
                                        line = line.replace(getChatColor(enchantName) + "▍ §7" + enchantName + " " + currentLevel, getChatColor(enchantName) + "▍ §7" + enchantName + " " + (currentLevel + levelToAdd));
                                        enchantFinded = true;
                                    }
                                    tmpLore.add(line);
                                }
                            }

                            if (!enchantFinded) {
                                tmpLore.add(getChatColor(enchantName) + "▍ §7" + enchantName + " " + (currentLevel + levelToAdd));
                            }

                            ItemMeta meta = itemInHand.getItemMeta();
                            List<String> initialLore = new ArrayList<>();
                            initialLore.addAll(tmpLore);
                            meta.setLore(initialLore);
                            itemInHand.setItemMeta(meta);

                            player.sendMessage(prefix + "Your item has been enchanted with " + levelToAdd + " §7level/s of §a§l" + enchantName);
                            player.sendMessage(prefix + "§aCost: §e" + main.newFormatNumber(tier1Cost, player) + " §7T1, §e" + main.newFormatNumber(tier2Cost, player) + " §7T2, §e" + main.newFormatNumber(tier3Cost, player) + " §7T3 Crystals");
                        } else {
                            player.sendMessage(prefix + "§cYou don't have enough crystals!");
                            player.sendMessage(prefix + "§cRequired: §e" + main.newFormatNumber(tier1Cost, player) + " §7T1, §e" + main.newFormatNumber(tier2Cost, player) + " §7T2, §e" + main.newFormatNumber(tier3Cost, player) + " §7T3");
                            player.sendMessage(prefix + "§cYou have: §e" + main.newFormatNumber(playerTier1, player) + " §7T1, §e" + main.newFormatNumber(playerTier2, player) + " §7T2, §e" + main.newFormatNumber(playerTier3, player) + " §7T3");
                        }
                        return; // Exit early for Dungeon Finder
                    }

                    if (levelToAdd > 0) {
                        if (blocksNeeded > 0) {
                            // IL -1 SERVE PER FAR SI CHE IL PRIMO LIVELLO SIA SENZA BLOCCHI, DAL SECONDO PARTE DAL PREZZO BASE
                            int blocksNeededForLevels = 0;
                            int levelThatPlayerCanAfford = 0;
                            if (levelToAdd > 1) {
                                for (int i = 1; i < levelToAdd; i++) {
                                    blocksNeededForLevels = (int) (blocksNeeded * (currentLevel + i - 1));
                                    if (playerBlocks >= blocksNeededForLevels) {
                                        levelThatPlayerCanAfford++;
                                    } else {
                                        break;
                                    }
                                }

                                levelToAdd = levelThatPlayerCanAfford;
                                if (levelToAdd == 0) {
                                    levelToAdd = 1;
                                }
                            }
                            blocksNeededForLevels = (int) (blocksNeeded * (currentLevel + levelToAdd - 1));
                            if (playerBlocks < blocksNeededForLevels) {
                                player.sendMessage(prefix + "§cYou need §l" + main.newFormatNumber(blocksNeededForLevels, player) + "§c blocks to enchant to this level");
                                return;
                            }
                            getEnchantData = getPrice(player, enchantName, currentLevel, levelToAdd, playerTokens);
                            price = getEnchantData[0];
                        }
                        if ((valuta.equalsIgnoreCase("Tokens") && playerTokens >= price && enchantTier == 0) || (valuta.equalsIgnoreCase("Crystals") && enchantTier > 0 && playerCrystals >= price) || (valuta.equalsIgnoreCase("BeaconPoints") && playerBeaconpoints >= price)) {
                            if (valuta.equalsIgnoreCase("Tokens")) {
                                teAPI.removeTokens(player, price);
                            } else if (valuta.equalsIgnoreCase("Crystals")) {
                                main.getDungeonSystem().removePoints(player, enchantTier, (long) price);
                            } else if (valuta.equalsIgnoreCase("BeaconPoints")) {
                                main.getBeaconPointSystem().removePoints(player, (long) price);
                            }
                            boolean enchantFinded = false;
                            if (lore != null) {
                                for (String line : lore) {
                                    if (line.contains(getChatColor(enchantName) + "▍ §7" + enchantName + " ") && !line.contains(" Merchant ")) {
                                        line = line.replace(getChatColor(enchantName) + "▍ §7" + enchantName + " " + currentLevel, getChatColor(enchantName) + "▍ §7" + enchantName + " " + (currentLevel + levelToAdd));
                                        enchantFinded = true;
                                    }
                                    newLore.add(line);
                                }
                            }
                            if (!enchantFinded) {
                                newLore.add(getChatColor(enchantName) + "▍ §7" + enchantName + " " + levelToAdd);
                            }
                    /*if (currentLevel + levelToAdd > 999) {
                        assert lore != null;
                        if (HiddenStringUtils.hasHiddenString(lore.get(lore.size() - 1))) {
                            String jsonString = HiddenStringUtils.extractHiddenString(lore.get(lore.size() - 1));
                            JsonObject object = new JsonParser().parse(jsonString).getAsJsonObject();
                            object.addProperty(enchantName, currentLevel + levelToAdd);
                            //if (object.get(enchantName) != null) {
                            //    AlphaBlockBreak.GetInstance().getLogger().info(object.toString() + " 1");
                            //
                            //    AlphaBlockBreak.GetInstance().getLogger().info(object.toString() + " 2");
                            //}
                            HiddenStringUtils.replaceHiddenString(lore.get(lore.size() - 1), object.toString());
                            newLore.add(HiddenStringUtils.encodeString(object.toString()));
                        } else {
                            JSONObject newJson = new JSONObject();
                            newJson.put(enchantName, currentLevel + levelToAdd);
                            newLore.add(HiddenStringUtils.encodeString(newJson.toString()));
                        }
                    }*/

                            player.sendMessage(prefix + "You bought §a§l" + levelToAdd + " §7level/s of §a§l" + enchantName + " §7for a total of §a§l" + mainClass.newFormatNumber(price, player) + " §7" + (!valuta.equalsIgnoreCase("Crystals") ? valuta : "Tier " + enchantTier + " Crystals"));
                            ItemMeta meta = itemInHand.getItemMeta();
                            //newLore = orderLore(newLore);
                            //newLore = checkLevelFormatted(newLore);
                            //newLore = orderLore(newLore);
                            meta.setLore(newLore);
                            addGlowing(meta);
                            itemInHand.setItemMeta(meta);
                            sortEnchant(player);
                            checkPick(player);
                            //player.updateInventory();
                        } else {
                            player.sendMessage(prefix + "§cYou don't have enough " + /*(enchantTier == 0 ? "Tokens" : "Crystals")*/valuta + ", you need §c§l" + mainClass.newFormatNumber(price, player) + (enchantTier == 0 ? "" : " §cTier " + enchantTier + " Crystals"));
                        }
                    } else {
                        player.sendMessage(prefix + "§aEnchant maxed out !");
                    }
                /*} else {
                    player.sendMessage(prefix + "§cYou need to reset your pickaxe in order to unlock this enchant !");
                }*/
            } else {
                player.sendMessage(prefix + "§cYou need an higher tier to enchant !");
            }
        }
    }

    public void setEnchant(Player player, String enchantName, int enchantLevel) {
        ItemStack itemInHand = player.getInventory().getItemInMainHand();
        if (itemInHand.getType().equals(XMaterial.DIAMOND_PICKAXE.parseMaterial()) || (itemInHand.getType().equals(XMaterial.DIAMOND_AXE.parseMaterial()) && enchantName.equalsIgnoreCase("Efficiency"))) {
            ItemMeta meta = itemInHand.getItemMeta();
            List<String> lore = meta.getLore();
            List<String> newLore = new ArrayList<>();

            if (lore != null && !lore.isEmpty()) {
                for (String line : lore) {
                    if (!line.contains(enchantName)) {
                        newLore.add(line);
                    }
                }
            }

            enchantName = bookManager.getEnchantName(enchantName);
            newLore.add(getChatColor(enchantName) + "▍ §7" + enchantName + " " + enchantLevel);
            meta.setLore(newLore);
            addGlowing(meta);
            itemInHand.setItemMeta(meta);
            player.updateInventory();
            player.sendMessage(prefix + "Your item has been enchanted with " + enchantLevel + " §7level/s of §a§l" + enchantName);
        }
    }

    public int getPlayerSpeedLevel(Player player) {
        int level = 1;

        User user = mainClass.getLuckPerms().getPlayerAdapter(Player.class).getUser(player);
        for (Node perm : user.getDistinctNodes()) {
            if (StringUtils.startsWith(perm.getKey(), "effect.speed.")) {
                level = Integer.parseInt(StringUtils.substringAfterLast(perm.getKey(), "."));
            }
        }

        return level;
    }

    public int getPlayerHasteLevel(Player player) {
        int level = 1;

        User user = mainClass.getLuckPerms().getPlayerAdapter(Player.class).getUser(player);
        for (Node perm : user.getDistinctNodes()) {
            if (StringUtils.startsWith(perm.getKey(), "effect.haste.")) {
                level = Integer.parseInt(StringUtils.substringAfterLast(perm.getKey(), "."));
            }
        }

        return level;
    }

    public void givePlayerEffect(Player player, String type, int multiplier) {
        if (StringUtils.equalsIgnoreCase(type, "Speed")) {
            player.removePotionEffect(PotionEffectType.SPEED);
            player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 999999999, multiplier - 1));
            mainClass.removePex(player, "effect.haste." + getPlayerSpeedLevel(player));
            mainClass.addPex(player, "effect.speed", 0, false);
            mainClass.addPex(player, "effect.speed." + multiplier, 0, false);

            player.sendMessage(prefix + "§aSpeed " + multiplier + " Activated");
        } else if (StringUtils.equalsIgnoreCase(type, "Haste")) {
            player.removePotionEffect(PotionEffectType.FAST_DIGGING);
            player.addPotionEffect(new PotionEffect(PotionEffectType.FAST_DIGGING, 999999999, multiplier - 1));
            mainClass.removePex(player, "effect.haste." + getPlayerHasteLevel(player));
            mainClass.addPex(player, "effect.haste", 0, false);
            mainClass.addPex(player, "effect.haste." + multiplier, 0, false);
            player.sendMessage(prefix + "§aHaste " + multiplier + " Activated");
        }
    }

    public void removePlayerEffect(Player player, String type) {
        if (StringUtils.equalsIgnoreCase(type, "Speed")) {
            player.removePotionEffect(PotionEffectType.SPEED);
            mainClass.removePex(player, "effect.speed");

            player.sendMessage(prefix + "§aSpeed Disabled");
        } else if (StringUtils.equalsIgnoreCase(type, "Haste")) {
            player.removePotionEffect(PotionEffectType.FAST_DIGGING);
            mainClass.removePex(player, "effect.haste");

            player.sendMessage(prefix + "§aHaste Disabled");
        }
    }

    public int getEnchantMaxLevel(Player player, String enchantName) {
        PlayerData data = mainClass.getMongoReader().getPlayerData(player.getUniqueId());

        int maxLevel = enchantList.get(enchantName).getMaxLevel();
        // NUOVA GESTIONE MAX LEVEL
        if (!enchantList.get(enchantName).isInfinite()) {
            if (enchantList.get(enchantName).getLevelsToUnlockPerReset() != -1 && data.getPickaxeResetAmount() != null && data.getPickaxeResetAmount() > 0) {
                maxLevel = maxLevel + (enchantList.get(enchantName).getLevelsToUnlockPerReset() * data.getPickaxeResetAmount());
            }
        }

        return maxLevel;
    }

    public void openEnchantMenu(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, "§e§lENCHANT §f| §7Menu");
        Main.FillBorder(gui);

        List<String> pickaxeLore = player.getInventory().getItemInMainHand().getItemMeta().getLore();
        ItemStack efficiency = getSlotItem("Efficiency", pickaxeLore, player);
        //ItemStack unbreaking = getSlotItem("Unbreaking", pickaxeLore, player);
        ItemStack fortune = getSlotItem("Fortune", pickaxeLore, player);
        //ItemStack speed = getSlotItem("Speed", pickaxeLore, player);
        //ItemStack haste = getSlotItem("Haste", pickaxeLore, player);
        ItemStack greed = getSlotItem("Greed", pickaxeLore, player);
        ItemStack tokenGreed = getSlotItem("Token Greed", pickaxeLore, player);
        ItemStack keyFinder = getSlotItem("Key Finder", pickaxeLore, player);
        ItemStack combo = getSlotItem("Combo", pickaxeLore, player);
        ItemStack xpGreed = getSlotItem("XPGreed", pickaxeLore, player);
        ItemStack blockMiner = getSlotItem("Block Miner", pickaxeLore, player);
        ItemStack randomizer = getSlotItem("Randomizer", pickaxeLore, player);
        ItemStack randomizerMerchant = getSlotItem("RandomizerMerchant", pickaxeLore, player);
        //ItemStack luckyblock = getSlotItem("LuckyBlock", pickaxeLore, player);
        ItemStack luckykey = getSlotItem("LuckyKey", pickaxeLore, player);
        ItemStack beacongreed = getSlotItem("BeaconGreed", pickaxeLore, player);
        ItemStack prestigePointFinder = getSlotItem("Prestige Point Finder", pickaxeLore, player);
        ItemStack voucherFinder = getSlotItem("Voucher Finder", pickaxeLore, player);
        ItemStack prestigeFinder = getSlotItem("Prestige Finder", pickaxeLore, player);
        ItemStack robotFinder = getSlotItem("Robot Finder", pickaxeLore, player);
        ItemStack blessing = getSlotItem("Blessing", pickaxeLore, player);
        ItemStack jackpot = getSlotItem("JackPot", pickaxeLore, player);
        ItemStack nuke = getSlotItem("Nuke", pickaxeLore, player);
        ItemStack jackHammer = getSlotItem("JackHammer", pickaxeLore, player);
        ItemStack spawnerShardFinder = getSlotItem("Spawner Shard Finder", pickaxeLore, player);
        ItemStack wonderland = getSlotItem("WonderLand", pickaxeLore, player);
        ItemStack timelapse = getSlotItem("Timelapse", pickaxeLore, player);
        ItemStack fracture = getSlotItem("Fracture", pickaxeLore, player);
        ItemStack fireRoad = getSlotItem("Fire Road", pickaxeLore, player);
        ItemStack explosive = getSlotItem("Explosive", pickaxeLore, player);
        ItemStack friendship = getSlotItem("FriendShip", pickaxeLore, player);
        ItemStack candyFinder = getSlotItem("Candy Finder", pickaxeLore, player);
        ItemStack armorPointFinder = getSlotItem("ArmorPoint Finder", pickaxeLore, player);
        ItemStack valueFinder = getSlotItem("ValueFinder", pickaxeLore, player);
        ItemStack meteor = getSlotItem("Meteor", pickaxeLore, player);

        ItemStack speedEffect = new ItemStack(Material.SPLASH_POTION);
        PotionMeta meta = (PotionMeta) speedEffect.getItemMeta();
        meta.addCustomEffect(new PotionEffect(PotionEffectType.SPEED, 1, getPlayerSpeedLevel(player)), true);
        meta.setDisplayName("§6§lSPEED");
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §7Gives you speed effect");
        lore.add("§7| §7You can change the level with §f/speedlevel [level]");
        lore.add("§7| §7Level goes from 1 to 5");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft click: " + (mainClass.hasPermissionFromLuckPerms(player, "effect.speed") ? "§c§lDISABLE" : "§a§lENABLE"));
        lore.add("");
        meta.setLore(lore);
        speedEffect.setItemMeta(meta);

        ItemStack hasteEffect = new ItemStack(Material.SPLASH_POTION);
        meta = (PotionMeta) hasteEffect.getItemMeta();
        meta.addCustomEffect(new PotionEffect(PotionEffectType.FAST_DIGGING, 1, getPlayerHasteLevel(player)), true);
        meta.setDisplayName("§e§lHASTE");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §7Gives you haste effect");
        lore.add("§7| §7You can change the level with §f/hastelevel [level]");
        lore.add("§7| §7Level goes from 1 to 10");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft click: " + (mainClass.hasPermissionFromLuckPerms(player, "effect.haste") ? "§c§lDISABLE" : "§a§lENABLE"));
        lore.add("");
        meta.setLore(lore);
        hasteEffect.setItemMeta(meta);

        boolean canReset = mainClass.getPickaxeResetManager().canResetPick(player);
        ItemStack resetPick = new ItemStack(Material.DIAMOND_PICKAXE);
        ItemMeta itemMeta = resetPick.getItemMeta();
        itemMeta.setDisplayName("§c§lPICK RESET");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §7If you have maxed out all the enchants");
        lore.add("§7| §7you will be able to §c§lRESET YOUR PICKAXE");
        lore.add("§7| §7except for ValueFinder, to increase the max level");
        lore.add("§7| §7for most of the enchants.");
        lore.add("");
        PlayerData data = mainClass.getMongoReader().getPlayerData(player.getUniqueId());
        if (data.getPickaxeResetAmount() != null && data.getPickaxeResetAmount() == PickaxeReset.MAX_RESET) {
            lore.add("§a§lMAX PICK RESET REACHED");
            lore.add("§7| §7You don't have any other Pick Reset available");
        } else {
            if (canReset) {
                lore.add("§a§lRESET AVAILABLE");
                lore.add("§7| §7Click me to reset your pick");
            } else {
                double totalTokensMissed = 0;
                lore.add("§c§lRESET NOT AVAILABLE");
                for (EnchantList enchant : enchantList.values()) {
                    if (enchant.getLevelsToUnlockPerReset() != -1 && !enchant.getEnchantName().equals("ValueFinder")) {
                        String enchantName = enchant.getEnchantName();
                        double missedLevels = mainClass.getPickaxeResetManager().missedLevelToResetPick(player, enchant.getEnchantName());
                        if (missedLevels > 0) {
                            double enchantPriceToMax = getPrice(player, enchantName, (long) mainClass.getEnchantLevel(player, enchantName), missedLevels, Double.MAX_VALUE)[0];
                            totalTokensMissed += enchantPriceToMax;
                            lore.add("§7| §7Missing §c" + missedLevels + "§7 level/s of " + getChatColor(enchantName) + enchantName + " §7§o(" + mainClass.newFormatNumber(enchantPriceToMax, player) + ")");
                        }
                    }
                }

                // controllo dei blocchi
                int currentReset = data.getPickaxeResetAmount() == null ? 0 : data.getPickaxeResetAmount();
                currentReset++; // controllo per il prossimo reset -> se a 0, saranno 50k, a 1 saranno 100k...
                int currentBlocks = mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getBlocksMined();
                if (currentBlocks <= currentReset * 50000) {
                    lore.add("");
                    lore.add("§7| §7Missing §c" + ((currentReset * 50000) - currentBlocks) + "§7 blocks");
                }
                if (totalTokensMissed > 0) {
                    lore.add("");
                    lore.add("§7| §7Tokens Needed: §c" + mainClass.newFormatNumber(totalTokensMissed, player));
                }
            }
        }
        itemMeta.setLore(lore);
        resetPick.setItemMeta(itemMeta);

        ItemStack pickResetRewards = new ItemStack(Material.BOOK);
        itemMeta = pickResetRewards.getItemMeta();
        itemMeta.setDisplayName("§c§lPICK RESET - PERKS");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lPERKS");
        lore.add("§7| §f1. §7Money Pouch Finder");
        lore.add("§7| §f2. §71.5x Key Boosters (Money and Token boosters)");
        lore.add("§7| §f3. §7Wonderland Cooldown Reduction");
        lore.add("§7| §f4. §7JackHammer Double Layer Effect");
        lore.add("§7| §f5. §7Value Finder Booster");
        lore.add("§7| §f6. §7Token Pouch Finder");
        lore.add("§7| §f7. §71.2x Block Base Value");
        lore.add("§7| §f8. §7Half Requirement for Milestones");
        lore.add("§7| §f9. §7Half Requirements for Skills");
        lore.add("§7| §f10. §kIDK WHAT TO GIVE LOL");
        lore.add("");
        lore.add("§7Reset/s Made: §f" + (data.getPickaxeResetAmount() == null ? 0 : data.getPickaxeResetAmount()));
        lore.add("");
        itemMeta.setLore(lore);
        pickResetRewards.setItemMeta(itemMeta);

        gui.setItem(2, speedEffect);
        gui.setItem(6, hasteEffect);

        gui.setItem(4, resetPick);
        gui.setItem(8, pickResetRewards);

        //gui.setItem(10, efficiency);
        //gui.setItem(12, unbreaking);
        gui.setItem(11, efficiency);
        gui.setItem(12, fortune);
        gui.setItem(14, tokenGreed);
        gui.setItem(15, blessing);
        //gui.setItem(16, blessing);

        gui.setItem(19, greed);
        gui.setItem(20, keyFinder);
        gui.setItem(21, combo);
        gui.setItem(23, xpGreed);
        gui.setItem(24, randomizer);
        gui.setItem(25, jackHammer);

        gui.setItem(27, blockMiner);
        gui.setItem(28, randomizerMerchant);
        //gui.setItem(31, luckyblock);
        gui.setItem(29, beacongreed);
        gui.setItem(30, valueFinder);
        gui.setItem(31, candyFinder);
        gui.setItem(32, voucherFinder);
        gui.setItem(33, prestigeFinder);
        gui.setItem(34, fracture);
        gui.setItem(35, friendship);

        gui.setItem(37, luckykey);
        gui.setItem(38, prestigePointFinder);
        gui.setItem(39, robotFinder);
        gui.setItem(40, jackpot);
        gui.setItem(41, nuke);
        gui.setItem(42, timelapse);
        gui.setItem(43, spawnerShardFinder);

        gui.setItem(47, wonderland);
        gui.setItem(48, fireRoad);
        gui.setItem(49, meteor);
        gui.setItem(50, explosive);
        gui.setItem(51, armorPointFinder);

        player.openInventory(gui);
    }

    public void openBeaconEnchantMenu(Player player) {
        Inventory gui = Bukkit.createInventory(null, 45, "§e§lENCHANT §f| §7Menu");
        Main.FillBorder(gui);

        List<String> pickaxeLore = player.getInventory().getItemInMainHand().getItemMeta().getLore();
        ItemStack beaconBomb = getSlotItem("Beacon Bomb", pickaxeLore, player);
        ItemStack laser = getSlotItem("Laser", pickaxeLore, player);
        ItemStack treasureHunter = getSlotItem("Treasure Hunter", pickaxeLore, player);
        //ItemStack timer = getSlotItem("Timer", pickaxeLore, player);
        ItemStack dragonEggFinder = getSlotItem("Dragon Egg Finder", pickaxeLore, player);
        //ItemStack haste = getSlotItem("Haste", pickaxeLore, player);

        ItemStack informations = new ItemStack(Material.BOOK);
        ItemMeta meta = informations.getItemMeta();
        meta.setDisplayName("§e§lINFORMATIONS");
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7Beacon Point Balance: §a§l" + mainClass.getBeaconPointSystem().getPoints(player));
        meta.setLore(lore);
        informations.setItemMeta(meta);

        gui.setItem(4, informations);
        //gui.setItem(13, haste);
        gui.setItem(20, beaconBomb);
        gui.setItem(21, laser);
        gui.setItem(23, treasureHunter);
        //gui.setItem(23, timer);
        gui.setItem(24, dragonEggFinder);

        player.openInventory(gui);
    }

    public void sendEnchantStatistics(Player player) {
        double allEnchantPrice = 0D;
        for (EnchantList enchant : enchantList.values()) {
            double totalPrice = ((enchant.getMaxLevel() * (enchant.getMaxLevel() + 1D)) * (enchant.getPrice() / 2));
            allEnchantPrice = allEnchantPrice + totalPrice;
            player.sendMessage("§7" + enchant.getEnchantName() + " (" + enchant.getMaxLevel() + "): Price per level: §a" + mainClass.newFormatNumber(enchant.getPrice(), player) + "§7, Total price: §a" + mainClass.newFormatNumber(totalPrice, player));
        }
        player.sendMessage("§7Total Enchant Price: §a" + mainClass.newFormatNumber(allEnchantPrice, player));
    }
}

/*

    // Test per utilizzo di regex
    player.sendMessage(itemInHand.getItemMeta().getLore().toString());
    String regexString = enchantName + "(.*?)" + " ";
    Pattern pattern = Pattern.compile(regexString);
    Matcher matcher = pattern.matcher(itemInHand.getItemMeta().getLore().toString());
    player.sendMessage(matcher.group(1));

 */