package it.masterzen.blockbreak;

import com.songoda.skyblock.api.SkyBlockAPI;
import com.songoda.skyblock.api.island.Island;
import com.songoda.skyblock.api.island.IslandRole;
import it.masterzen.MongoDB.CustomCropData;
import it.masterzen.MongoDB.PlayerData;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.BlockState;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.material.Crops;

import java.util.ArrayList;
import java.util.List;

import java.util.concurrent.ThreadLocalRandom;

/**
 * Manages custom crop system including placement, harvesting, and rewards
 */
public class CustomCropManager implements Listener {

    private final String prefix = "§e§lCUSTOM CROPS §8»§7 ";
    private final AlphaBlockBreak mainClass;

    public CustomCropManager(AlphaBlockBreak plugin) {
        this.mainClass = plugin;
    }

    @EventHandler
    public void onBlockPlace(BlockPlaceEvent event) {
        Player player = event.getPlayer();
        Block block = event.getBlock();
        ItemStack item = event.getItemInHand();

        // Check if it's a custom crop seed
        if (!isCustomCropSeed(item)) {
            return;
        }

        // Validate island permissions
        if (!canPlaceCropOnIsland(player, block)) {
            event.setCancelled(true);
            player.sendMessage(prefix + "§cYou can only plant custom crops on your island!");
            return;
        }

        // Get crop type from seed
        String cropType = getCropTypeFromSeed(item);
        if (cropType == null) {
            event.setCancelled(true);
            return;
        }

        // Get island information
        Island island = SkyBlockAPI.getIslandManager().getIsland(player);
        if (island == null) {
            event.setCancelled(true);
            player.sendMessage(prefix + "§cYou must be on an island to plant custom crops!");
            return;
        }

        // Create and save custom crop data
        CustomCropData cropData = new CustomCropData(
                block.getWorld().getName(),
                block.getX(),
                block.getY(),
                block.getZ(),
                player.getUniqueId().toString(),
                island.getIslandUUID().toString(),
                cropType
        );

        mainClass.getMongoReader().saveCustomCrop(cropData);
        player.sendMessage(prefix + "You planted a custom " + cropType + " crop!");
    }

    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Player player = event.getPlayer();
        Block block = event.getBlock();

        // Quick checks first - verify it's a crop block type
        if (!isCropBlock(block)) {
            return; // Not a crop block at all
        }

        // Quick island check - must be on an island
        Island island = SkyBlockAPI.getIslandManager().getIslandAtLocation(block.getLocation());
        if (island == null) {
            return; // Not on an island
        }

        // Now check database for custom crop (more expensive operation)
        CustomCropData cropData = mainClass.getMongoReader().getCustomCrop(
                block.getWorld().getName(),
                block.getX(),
                block.getY(),
                block.getZ()
        );

        if (cropData == null) {
            return; // Not a custom crop
        }

        // Cancel the event to prevent normal drops and handle everything manually
        event.setCancelled(true);

        // Validate island permissions
        if (!canHarvestCropOnIsland(player, block, cropData)) {
            player.sendMessage(prefix + "§cYou don't have permission to harvest this crop!");
            return;
        }

        // Check if crop is fully grown
        if (isCropFullyGrown(block)) {
            // Give rewards and replant
            giveHarvestRewards(player, cropData.getCropType());
            replantCrop(block, cropData.getCropType());
            // player.sendMessage(prefix + "You harvested a " + cropData.getCropType() + " crop and received rewards!");
        } else {
            // Crop is not fully grown - don't remove it, just show instruction message
            player.sendMessage(prefix + "§cThis " + cropData.getCropType() + " crop is not fully grown yet!");
            player.sendMessage(prefix + "§7To remove an unripe crop, §fSneak §7and §fLeft Click §7on it.");
        }
    }

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();

        // Check if player is using bone meal
        if (event.getAction() != Action.RIGHT_CLICK_BLOCK ||
            event.getItem() == null ||
            event.getItem().getType() != XMaterial.BONE_MEAL.parseMaterial()) {
            return;
        }

        Block block = event.getClickedBlock();
        if (block == null) {
            return;
        }

        // Quick check - is it a crop block?
        if (!isCropBlock(block)) {
            return;
        }

        // Check if it's a custom crop (database query)
        CustomCropData cropData = mainClass.getMongoReader().getCustomCrop(
                block.getWorld().getName(),
                block.getX(),
                block.getY(),
                block.getZ()
        );

        if (cropData == null) {
            return; // Not a custom crop, allow normal bone meal usage
        }

        // It's a custom crop - check permissions
        if (!player.isOp()) {
            event.setCancelled(true);
            player.sendMessage(prefix + "§cYou cannot use bone meal on custom crops!");
            player.sendMessage(prefix + "§7Custom crops grow naturally and auto-replant when harvested.");
        }
        // If player is OP, allow bone meal usage (don't cancel event)
    }

    @EventHandler
    public void onPlayerSneakClick(PlayerInteractEvent event) {
        Player player = event.getPlayer();

        // Check if player is sneaking and left clicking a block
        if (!player.isSneaking() || event.getAction() != Action.LEFT_CLICK_BLOCK) {
            return;
        }

        Block block = event.getClickedBlock();
        if (block == null) {
            return;
        }

        // Quick check - is it a crop block?
        if (!isCropBlock(block)) {
            return;
        }

        // Quick island check - must be on an island
        Island island = SkyBlockAPI.getIslandManager().getIslandAtLocation(block.getLocation());
        if (island == null) {
            return; // Not on an island
        }

        // Check database for custom crop
        CustomCropData cropData = mainClass.getMongoReader().getCustomCrop(
                block.getWorld().getName(),
                block.getX(),
                block.getY(),
                block.getZ()
        );

        if (cropData == null) {
            return; // Not a custom crop
        }

        // Cancel the event to prevent normal block breaking
        event.setCancelled(true);

        // Validate island permissions
        if (!canHarvestCropOnIsland(player, block, cropData)) {
            player.sendMessage(prefix + "§cYou don't have permission to harvest this crop!");
            return;
        }

        // Check if crop is fully grown - if so, treat as normal harvest
        if (isCropFullyGrown(block)) {
            // Give rewards and replant
            giveHarvestRewards(player, cropData.getCropType());
            replantCrop(block, cropData.getCropType());
            return;
        }

        // Crop is not fully grown - allow removal with sneak + click
        returnSeedToPlayer(player, cropData.getCropType());
        mainClass.getMongoReader().removeCustomCrop(
                block.getWorld().getName(),
                block.getX(),
                block.getY(),
                block.getZ()
        );
        block.setType(XMaterial.AIR.parseMaterial()); // Clear the block
        player.sendMessage(prefix + "You removed an unripe " + cropData.getCropType() + " crop and got your seed back!");
    }

    /**
     * Checks if an item is a custom crop seed
     */
    private boolean isCustomCropSeed(ItemStack item) {
        if (item == null || !item.hasItemMeta() || !item.getItemMeta().hasDisplayName()) {
            return false;
        }

        String displayName = item.getItemMeta().getDisplayName();
        return displayName.contains("§6§lCUSTOM") &&
               (displayName.contains("WHEAT") || displayName.contains("CARROT") || displayName.contains("POTATO") || displayName.contains("BEETROOT"));
    }

    /**
     * Gets crop type from seed item
     */
    private String getCropTypeFromSeed(ItemStack item) {
        if (!item.hasItemMeta() || !item.getItemMeta().hasDisplayName()) {
            return null;
        }

        String displayName = item.getItemMeta().getDisplayName();
        if (displayName.contains("WHEAT")) {
            return "wheat";
        } else if (displayName.contains("CARROT")) {
            return "carrot";
        } else if (displayName.contains("POTATO")) {
            return "potato";
        } else if (displayName.contains("BEETROOT")) {
            return "beetroot";
        }
        return null;
    }

    /**
     * Checks if player can place crops on this island
     */
    private boolean canPlaceCropOnIsland(Player player, Block block) {
        Island island = SkyBlockAPI.getIslandManager().getIslandAtLocation(block.getLocation());
        if (island == null) {
            return false;
        }

        // Check if player is owner or has operator permissions
        return island.hasRole(player.getUniqueId(), IslandRole.OWNER) || 
               island.hasRole(player.getUniqueId(), IslandRole.OPERATOR) ||
               player.isOp();
    }

    /**
     * Checks if player can harvest crops on this island
     */
    private boolean canHarvestCropOnIsland(Player player, Block block, CustomCropData cropData) {
        Island island = SkyBlockAPI.getIslandManager().getIslandAtLocation(block.getLocation());
        if (island == null) {
            return false;
        }

        // Check if player is owner, operator, or member of the island
        return island.hasRole(player.getUniqueId(), IslandRole.OWNER) || 
               island.hasRole(player.getUniqueId(), IslandRole.OPERATOR) ||
               island.hasRole(player.getUniqueId(), IslandRole.MEMBER) ||
               player.isOp();
    }

    /**
     * Quick check if block is a crop type (before expensive database query)
     */
    private boolean isCropBlock(Block block) {
        return block.getType() == Material.CROPS ||
               block.getType() == XMaterial.CARROTS.parseMaterial() ||
               block.getType() == XMaterial.POTATOES.parseMaterial();
    }

    /**
     * Checks if a crop block is fully grown
     */
    private boolean isCropFullyGrown(Block block) {
        // Use XMaterial for better compatibility
        if (block.getType() == Material.CROPS ||
            block.getType() == XMaterial.CARROTS.parseMaterial() ||
            block.getType() == XMaterial.POTATOES.parseMaterial()) {

            // Check block data for growth stage (7 = fully grown)
            byte data = block.getData();
            return data == 7;
        }
        return false;
    }

    /**
     * Gives harvest rewards to player based on crop type
     */
    private void giveHarvestRewards(Player player, String cropType) {
        switch (cropType.toLowerCase()) {
            case "wheat":
                // Give 5-15 Fortune levels
                int fortuneLevels = ThreadLocalRandom.current().nextInt(11) + 5; // 5-15
                giveEnchantmentBook(player, "Fortune", fortuneLevels);
                break;

            case "carrot":
                // Give 5-10 Token Greed levels
                int tokenGreedLevels = ThreadLocalRandom.current().nextInt(6) + 5; // 5-10
                giveEnchantmentBook(player, "Token Greed", tokenGreedLevels);
                break;

            case "potato":
                // Give 5% money/token boost
                applyBoostReward(player);
                break;

            case "beetroot":
                // Give ALL THREE rewards (wheat + carrot + potato)
                player.sendMessage(prefix + "§a§lBEETROOT HARVEST! §7Giving all seed rewards...");

                // Wheat reward: 5-15 Fortune levels
                int beetrootFortuneLevels = ThreadLocalRandom.current().nextInt(11) + 5; // 5-15
                giveEnchantmentBook(player, "Fortune", beetrootFortuneLevels);

                // Carrot reward: 5-10 Token Greed levels
                int beetrootTokenGreedLevels = ThreadLocalRandom.current().nextInt(6) + 5; // 5-10
                giveEnchantmentBook(player, "Token Greed", beetrootTokenGreedLevels);

                // Potato reward: 5% money/token boost
                applyBoostReward(player);

                player.sendMessage(prefix + "§6§lTriple rewards received from beetroot harvest!");
                break;
        }
    }

    /**
     * Gives an enchantment book to the player, merging with existing books if possible
     */
    private void giveEnchantmentBook(Player player, String enchantName, int levels) {
        // Use the enhanced book system with merging functionality
        Book bookManager = new Book();
        bookManager.giveEnchantBookWithMerging(enchantName, levels, player);
    }

    /**
     * Applies 5% money OR token boost to player (randomly chosen)
     */
    private void applyBoostReward(Player player) {
        PlayerData data = mainClass.getMongoReader().getPlayerData(player.getUniqueId());

        // Randomly choose between money or token boost (50/50 chance)
        double boostAmount = 5.0;
        boolean isMoneyBoost = ThreadLocalRandom.current().nextBoolean();

        if (isMoneyBoost) {
            // Apply money boost
            data.setKeysMoneyBooster((data.getKeysMoneyBooster() == null ? 0 : data.getKeysMoneyBooster()) + boostAmount);
            mainClass.getMongoReader().setPlayerMoneyBoosterInMap(player, data.getKeysMoneyBooster());
            player.sendMessage(prefix + "You received §6§l5%§7 Money Booster!");
        } else {
            // Apply token boost
            data.setKeysTokenBooster((data.getKeysTokenBooster() == null ? 0 : data.getKeysTokenBooster()) + boostAmount);
            mainClass.getMongoReader().setPlayerTokenBoosterInMap(player, data.getKeysTokenBooster());
            player.sendMessage(prefix + "You received §6§l5%§7 Token Booster!");
        }
    }

    /**
     * Replants the crop after harvest
     */
    private void replantCrop(Block block, String cropType) {
        // Set the block back to the appropriate crop type at growth stage 0
        Material cropMaterial;
        switch (cropType.toLowerCase()) {
            case "wheat":
                cropMaterial = Material.CROPS;
                break;
            case "carrot":
                cropMaterial = XMaterial.CARROTS.parseMaterial();
                break;
            case "potato":
                cropMaterial = XMaterial.POTATOES.parseMaterial();
                break;
            case "beetroot":
                cropMaterial = XMaterial.BEETROOTS.parseMaterial();
                break;
            default:
                return;
        }

        block.setType(cropMaterial);
        block.setData((byte) 0); // Set to initial growth stage (0)
    }

    /**
     * Returns the seed to the player's inventory
     */
    private void returnSeedToPlayer(Player player, String cropType) {
        ItemStack seed = createCustomSeed(cropType);
        if (seed != null) {
            player.getInventory().addItem(seed);
        }
    }

    /**
     * Creates a custom seed item
     */
    private ItemStack createCustomSeed(String cropType) {
        Material seedMaterial;
        String displayName;

        switch (cropType.toLowerCase()) {
            case "wheat":
                seedMaterial = XMaterial.WHEAT_SEEDS.parseMaterial();
                displayName = "§6§lCUSTOM WHEAT §7Seed";
                break;
            case "carrot":
                seedMaterial = XMaterial.CARROT.parseMaterial();
                displayName = "§6§lCUSTOM CARROT §7Seed";
                break;
            case "potato":
                seedMaterial = XMaterial.POTATO.parseMaterial();
                displayName = "§6§lCUSTOM POTATO §7Seed";
                break;
            case "beetroot":
                seedMaterial = XMaterial.BEETROOT_SEEDS.parseMaterial();
                displayName = "§6§lCUSTOM BEETROOT §7Seed";
                break;
            default:
                return null;
        }

        ItemStack seed = new ItemStack(seedMaterial, 1);
        ItemMeta meta = seed.getItemMeta();
        meta.setDisplayName(displayName);
        seed.setItemMeta(meta);

        return seed;
    }
}
