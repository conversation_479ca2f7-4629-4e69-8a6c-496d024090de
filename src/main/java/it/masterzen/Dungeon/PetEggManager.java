package it.masterzen.Dungeon;

import it.masterzen.blockbreak.NBTItem;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Manages Pet Egg functionality including block tracking and hatching
 */
public class PetEggManager implements Listener {

    private final String prefix = "§6§lPET EGG §8»§7 ";

    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Player player = event.getPlayer();
        ItemStack mainHand = player.getInventory().getItemInMainHand();
        
        // Check if player is holding a pet egg
        if (!isPetEgg(mainHand)) {
            return;
        }
        
        NBTItem nbtItem = new NBTItem(mainHand);
        Object blocksToHatchObj = nbtItem.getCustomMeta("blocksToHatch");
        Object blocksBrokenObj = nbtItem.getCustomMeta("blocksBroken");
        
        if (blocksToHatchObj == null || blocksBrokenObj == null) {
            return;
        }
        
        int blocksToHatch = ((Number) blocksToHatchObj).intValue();
        int blocksBroken = ((Number) blocksBrokenObj).intValue();
        
        // Increment blocks broken
        blocksBroken++;
        nbtItem.setCustomMeta("blocksBroken", blocksBroken);
        
        // Update lore to show progress
        updatePetEggLore(nbtItem, blocksBroken, blocksToHatch);
        
        // Replace item in player's hand
        player.getInventory().setItemInMainHand(nbtItem.getItem());
        
        // Check if ready to hatch
        if (blocksBroken >= blocksToHatch) {
            hatchPetEgg(player);
        }
    }

    /**
     * Checks if an item is a pet egg
     */
    private boolean isPetEgg(ItemStack item) {
        if (item == null || item.getType() != Material.MONSTER_EGG) {
            return false;
        }
        
        if (!item.hasItemMeta() || !item.getItemMeta().hasDisplayName()) {
            return false;
        }
        
        String displayName = item.getItemMeta().getDisplayName();
        if (!displayName.equals("§6§lPET EGG")) {
            return false;
        }
        
        // Check NBT data
        NBTItem nbtItem = new NBTItem(item);
        Object isPetEggObj = nbtItem.getCustomMeta("isPetEgg");
        return isPetEggObj != null && (Boolean) isPetEggObj;
    }

    /**
     * Updates the pet egg lore with current progress
     */
    private void updatePetEggLore(NBTItem nbtItem, int blocksBroken, int blocksToHatch) {
        ItemStack item = nbtItem.getItem();
        ItemMeta meta = item.getItemMeta();
        
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fA mysterious egg that will hatch");
        lore.add("§7| §finto a random pet after breaking");
        lore.add("§7| §e35,000 blocks §fwhile holding it!");
        lore.add("");
        lore.add("§e§lPROGRESS");
        lore.add("§e| §fBlocks Broken: §a" + blocksBroken + "§f/§e" + blocksToHatch);
        
        int remaining = Math.max(0, blocksToHatch - blocksBroken);
        if (remaining == 0) {
            lore.add("§e| §a§lREADY TO HATCH!");
        } else {
            lore.add("§e| §fRemaining: §c" + remaining + " blocks");
        }
        
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fHold this egg while mining");
        lore.add("§6| §fto progress towards hatching!");
        lore.add("");
        
        // Preserve NBT data by using NBTItem's setLore method
        nbtItem.setLore(lore);
    }

    /**
     * Hatches the pet egg and gives the player a random pet
     */
    private void hatchPetEgg(Player player) {
        // Remove the pet egg
        player.getInventory().setItemInMainHand(null);
        
        // Give random pet
        giveRandomPet(player);
        
        // Send success message
        player.sendMessage(prefix + "§a§lCongratulations! Your pet egg has hatched!");
        player.sendMessage(prefix + "§eYou received a random pet! Check your helmet slot.");
    }

    /**
     * Gives a random pet to the player (level 1)
     */
    public void giveRandomPet(Player player) {
        String[] petTypes = {"TOKEN", "MONEY", "JACKHAMMER", "WONDERLAND"};
        String selectedType = petTypes[ThreadLocalRandom.current().nextInt(petTypes.length)];
        
        ItemStack pet = createPet(selectedType, 1);
        
        // Try to equip the pet in helmet slot, or give to inventory if occupied
        ItemStack currentHelmet = player.getInventory().getHelmet();
        if (currentHelmet == null || currentHelmet.getType() == Material.AIR) {
            player.getInventory().setHelmet(pet);
            player.sendMessage(prefix + "§aYour new §e" + selectedType + " Pet §ahas been equipped!");
        } else {
            player.getInventory().addItem(pet);
            player.sendMessage(prefix + "§aYour new §e" + selectedType + " Pet §ahas been added to your inventory!");
            player.sendMessage(prefix + "§7(Your helmet slot was occupied)");
        }
    }

    /**
     * Creates a pet helmet item
     */
    private ItemStack createPet(String petType, int level) {
        ItemStack pet = new ItemStack(Material.LEATHER_HELMET);
        ItemMeta meta = pet.getItemMeta();
        
        // Set display name based on pet type
        String displayName;
        String colorCode;
        switch (petType.toUpperCase()) {
            case "TOKEN":
                displayName = "§a§lTOKEN§7-Pet";
                colorCode = "§a";
                break;
            case "MONEY":
                displayName = "§e§lMONEY§7-Pet";
                colorCode = "§e";
                break;
            case "JACKHAMMER":
                displayName = "§6§lJACKHAMMER§7-Pet";
                colorCode = "§6";
                break;
            case "WONDERLAND":
                displayName = "§e§lWONDERLAND§7-Pet";
                colorCode = "§e";
                break;
            default:
                displayName = "§7§lPET";
                colorCode = "§7";
                break;
        }
        
        meta.setDisplayName(displayName);
        
        // Create lore with level 1 stats
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lLEVEL: §f" + level);
        lore.add("");
        
        // Add type-specific boosts for level 1
        switch (petType.toUpperCase()) {
            case "TOKEN":
                lore.add("§a§lBOOSTS");
                lore.add("§aTOKEN GREED BOOST » §f5%");
                lore.add("§aNUKE BOOST » §f3%");
                break;
            case "MONEY":
                lore.add("§e§lBOOSTS");
                lore.add("§eAUTOSELL BOOST » §f5%");
                lore.add("§eJACKHAMMER BOOST » §f3%");
                break;
            case "JACKHAMMER":
                lore.add("§6§lBOOSTS");
                lore.add("§6JACKHAMMER BOOST » §f8%");
                break;
            case "WONDERLAND":
                lore.add("§e§lBOOSTS");
                lore.add("§eWONDERLAND BOOST » §f5%");
                break;
        }
        
        lore.add("");
        lore.add("§7§lUSAGE");
        lore.add("§7| §fEquip in helmet slot to activate");
        lore.add("§7| §fboosts while mining!");
        lore.add("");
        
        meta.setLore(lore);
        pet.setItemMeta(meta);
        
        return pet;
    }
}
