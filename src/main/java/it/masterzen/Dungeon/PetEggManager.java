package it.masterzen.Dungeon;

import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.NBTItem;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Manages Pet Egg functionality including block tracking and hatching
 */
public class PetEggManager implements Listener {

    private final String prefix = "§6§lPET EGG §8»§7 ";
    private final AlphaBlockBreak mainClass;

    public PetEggManager(AlphaBlockBreak mainClass) {
        this.mainClass = mainClass;
    }

    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Player player = event.getPlayer();
        ItemStack mainHand = player.getInventory().getItemInMainHand();
        
        // Check if player is holding a pet egg
        if (!isPetEgg(mainHand)) {
            return;
        }
        
        NBTItem nbtItem = new NBTItem(mainHand);
        Object blocksToHatchObj = nbtItem.getCustomMeta("blocksToHatch");
        Object blocksBrokenObj = nbtItem.getCustomMeta("blocksBroken");
        
        if (blocksToHatchObj == null || blocksBrokenObj == null) {
            return;
        }
        
        int blocksToHatch = ((Number) blocksToHatchObj).intValue();
        int blocksBroken = ((Number) blocksBrokenObj).intValue();
        
        // Increment blocks broken
        blocksBroken++;
        nbtItem.setCustomMeta("blocksBroken", blocksBroken);
        
        // Update lore to show progress
        updatePetEggLore(nbtItem, blocksBroken, blocksToHatch);
        
        // Replace item in player's hand
        player.getInventory().setItemInMainHand(nbtItem.getItem());
        
        // Check if ready to hatch
        if (blocksBroken >= blocksToHatch) {
            hatchPetEgg(player);
        }
    }

    /**
     * Checks if an item is a pet egg
     */
    private boolean isPetEgg(ItemStack item) {
        if (item == null || item.getType() != Material.MONSTER_EGG) {
            return false;
        }
        
        if (!item.hasItemMeta() || !item.getItemMeta().hasDisplayName()) {
            return false;
        }
        
        String displayName = item.getItemMeta().getDisplayName();
        if (!displayName.equals("§6§lPET EGG")) {
            return false;
        }
        
        // Check NBT data
        NBTItem nbtItem = new NBTItem(item);
        Object isPetEggObj = nbtItem.getCustomMeta("isPetEgg");
        return isPetEggObj != null && (Boolean) isPetEggObj;
    }

    /**
     * Updates the pet egg lore with current progress
     */
    private void updatePetEggLore(NBTItem nbtItem, int blocksBroken, int blocksToHatch) {
        ItemStack item = nbtItem.getItem();
        ItemMeta meta = item.getItemMeta();
        
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fA mysterious egg that will hatch");
        lore.add("§7| §finto a random pet after breaking");
        lore.add("§7| §e35,000 blocks §fwhile holding it!");
        lore.add("");
        lore.add("§e§lPROGRESS");
        lore.add("§e| §fBlocks Broken: §a" + blocksBroken + "§f/§e" + blocksToHatch);
        
        int remaining = Math.max(0, blocksToHatch - blocksBroken);
        if (remaining == 0) {
            lore.add("§e| §a§lREADY TO HATCH!");
        } else {
            lore.add("§e| §fRemaining: §c" + remaining + " blocks");
        }
        
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fHold this egg while mining");
        lore.add("§6| §fto progress towards hatching!");
        lore.add("");
        
        // Preserve NBT data by using NBTItem's setLore method
        nbtItem.setLore(lore);
    }

    /**
     * Hatches the pet egg and gives the player a random pet
     */
    private void hatchPetEgg(Player player) {
        // Remove the pet egg
        player.getInventory().setItemInMainHand(null);
        
        // Give random pet
        mainClass.giveRandomPet(player);
        
        // Send success message
        player.sendMessage(prefix + "§a§lCongratulations! Your pet egg has hatched!");
    }
}
